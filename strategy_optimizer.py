# strategy_optimizer.py
"""Specialized StrategyOptimizer that focuses each strategy on its own pattern, using MarketSimulator to stress-test and evolve weaknesses.
"""
from __future__ import annotations

import json
import time
import random
from typing import Dict, Tuple, List, Generator, Any

import numpy as np
from joblib import Parallel, delayed
from scipy.optimize import differential_evolution

from strategy import Strategy, TimeFrame
from parameter_config import VALID_STRATEGY_PARAMETERS

# Parameter ranges for optimization
PARAMETER_RANGES = {
    # Trend Following
    'MOVING_AVERAGE_SHORT': (5, 50),
    'MOVING_AVERAGE_LONG': (20, 200),
    'TREND_STRENGTH_THRESHOLD': (0.1, 0.9),
    'TREND_CONFIRMATION_PERIOD': (3, 20),
    'MOMENTUM_FACTOR': (0.5, 2.0),
    'BREAKOUT_LEVEL': (0.01, 0.1),
    'TRAILING_STOP': (0.01, 0.05),

    # Mean Reversion
    'MEAN_WINDOW': (10, 100),
    'STD_MULTIPLIER': (1.0, 3.0),
    'MEAN_REVERSION_THRESHOLD': (0.1, 0.8),
    'ENTRY_DEVIATION': (1.0, 3.0),
    'EXIT_DEVIATION': (0.5, 2.0),
    'BOLLINGER_PERIOD': (10, 50),
    'BOLLINGER_STD': (1.5, 2.5),

    # Momentum
    'MOMENTUM_PERIOD': (5, 30),
    'MOMENTUM_THRESHOLD': (0.1, 0.5),
    'RSI_PERIOD': (10, 30),
    'RSI_OVERBOUGHT': (60, 80),
    'RSI_OVERSOLD': (20, 40),
    'ACCELERATION_FACTOR': (0.01, 0.05),
    'MAX_ACCELERATION': (0.1, 0.3),
    'MACD_FAST': (8, 15),
    'MACD_SLOW': (20, 30),
    'MACD_SIGNAL': (7, 12),

    # Breakout
    'BREAKOUT_PERIOD': (10, 50),
    'BREAKOUT_THRESHOLD': (0.01, 0.1),
    'VOLUME_CONFIRMATION_MULT': (1.2, 3.0),
    'CONSOLIDATION_PERIOD': (5, 30),
    'SUPPORT_RESISTANCE_LOOKBACK': (20, 100),
    'BREAKOUT_CONFIRMATION_CANDLES': (1, 5),
    'ATR_PERIOD': (10, 30),

    # Volatility Clustering
    'VOLATILITY_WINDOW': (10, 50),
    'HIGH_VOLATILITY_THRESHOLD': (0.02, 0.1),
    'LOW_VOLATILITY_THRESHOLD': (0.005, 0.02),
    'GARCH_LAG': (1, 5),
    'ATR_MULTIPLIER': (1.0, 3.0),
    'VOLATILITY_BREAKOUT_THRESHOLD': (0.01, 0.05),
    'VOLATILITY_MEAN_PERIOD': (20, 100),

    # Statistical Arbitrage
    'LOOKBACK_PERIOD': (50, 200),
    'Z_SCORE_THRESHOLD': (1.5, 3.0),
    'CORRELATION_THRESHOLD': (0.7, 0.95),
    'HALF_LIFE': (10, 100),
    'HEDGE_RATIO': (0.5, 2.0),
    'ENTRY_THRESHOLD': (1.0, 2.5),
    'EXIT_THRESHOLD': (0.1, 1.0),
    'WINDOW_SIZE': (30, 150),
    'MIN_CORRELATION': (0.6, 0.9),
    'COINTEGRATION_THRESHOLD': (0.01, 0.1),

    # Sentiment Analysis
    'POSITIVE_SENTIMENT_THRESHOLD': (0.6, 0.9),
    'NEGATIVE_SENTIMENT_THRESHOLD': (0.1, 0.4),
    'SENTIMENT_WINDOW': (5, 30),
    'SENTIMENT_IMPACT_WEIGHT': (0.1, 0.8),
    'NEWS_IMPACT_DECAY': (0.1, 0.9),
    'SENTIMENT_SMOOTHING_FACTOR': (0.1, 0.5),
    'SENTIMENT_VOLUME_THRESHOLD': (1.2, 3.0),
    'SENTIMENT_MOMENTUM_PERIOD': (3, 15)
}

class StrategyOptimizer:
    """Optimises a single Strategy instance by stress-testing its specific pattern.

    Uses configurable evolution loops (genetic algorithm or differential evolution) to improve
    each strategy across multiple simulated market scenarios. Resource usage is controlled via
    config parameters such as population size, number of generations, and optimizer scenarios.

    ULTRA-FAST MODE: Optimized for 30-minute completion with 4-core system constraints.
    """

    def __init__(self, config: Any, market_simulator: Any, strategies: Dict[TimeFrame, Dict[str, Strategy]]):
        self.config = config
        self.market_simulator = market_simulator
        self.strategies = strategies

        # ULTRA-FAST OPTIMIZATION: 30-minute target configuration
        self.ultra_fast_mode = getattr(config, 'ultra_fast_mode', True)
        self.ultra_fast_config = {
            'max_iterations': 15,           # Reduced from 50+ to 15
            'population_size': 8,           # Reduced from 20+ to 8
            'n_scenarios': 2,               # Reduced from 5+ to 2
            'parallel_workers': 4,          # Match 4-core system
            'early_stopping_patience': 3,  # Stop if no improvement for 3 iterations
            'convergence_threshold': 0.001, # Stop if improvement < 0.1%
            'fast_evaluation_mode': True,  # Skip expensive calculations
            'reduced_market_complexity': True,  # Simplified market simulation
            'cache_enabled': True,          # Cache simulation results
            'micro_optimization': True     # Focus on most impactful parameters only
        }

        # Performance tracking for 30-minute target
        self.optimization_start_time = None
        self.target_completion_time = 30 * 60  # 30 minutes in seconds
        self.performance_cache = {}  # Cache for repeated simulations

    def optimize_strategy(self, strategy: Strategy) -> Tuple[Strategy, float]:
        """Optimize parameters so that this strategy becomes stronger in its own pattern domain.

        Returns
        -------
        Tuple[Strategy, float]
            The same Strategy (with updated parameters and .performance) and its averaged Sharpe.
        """
        param_ranges = self._get_strategy_param_ranges(strategy)
        if not param_ranges:
            raise ValueError(f"No parameter ranges for strategy patterns: {strategy.favored_patterns}")

        # Config-driven values
        n_scenarios = self.config.BASE_PARAMS.get("OPTIMIZER_SCENARIOS", 3)
        use_genetic = self.config.BASE_PARAMS.get("USE_GENETIC_ALGO", False)
        ga_generations = self.config.BASE_PARAMS.get("GENETIC_ALGORITHM_GENERATIONS", 20)
        ga_population = self.config.BASE_PARAMS.get("GENETIC_ALGORITHM_POPULATION", 50)
        mutation_rate = self.config.BASE_PARAMS.get("MUTATION_RATE", 0.2)
        crossover_rate = self.config.BASE_PARAMS.get("CROSSOVER_RATE", 0.7)
        de_maxiter = self.config.BASE_PARAMS.get("OPTIMIZER_MAXITER", 50)
        de_popsize = self.config.BASE_PARAMS.get("OPTIMIZER_POPSZ", 15)

        param_names = list(param_ranges.keys())
        bounds = [param_ranges[name] for name in param_names]

        # Fitness function: average Sharpe across scenarios (we maximize Sharpe)
        def fitness(params: np.ndarray) -> float:
            temp = strategy.clone()
            param_dict = {name: float(val) for name, val in zip(param_names, params)}
            temp.update_parameters(param_dict)

            sharpe_vals: List[float] = []
            for _ in range(n_scenarios):
                perf = self.market_simulator.run_simulation(temp)
                sharpe_vals.append(perf.get("sharpe_ratio", 0.0))
            return float(np.mean(sharpe_vals))

        if use_genetic:
            # Initialize random population within bounds
            population = []  # List[np.ndarray]
            for _ in range(ga_population):
                indiv = np.array([random.uniform(low, high) for low, high in bounds])
                population.append(indiv)

            # Evaluate initial population
            fitnesses = [fitness(ind) for ind in population]

            for gen in range(ga_generations):
                new_population = []
                # Elitism: carry over top 2
                elites = sorted(zip(population, fitnesses), key=lambda x: x[1], reverse=True)[:2]
                for indiv, fit in elites:
                    new_population.append(indiv)

                # Fill rest
                while len(new_population) < ga_population:
                    # Selection: tournament of 3
                    contenders = random.sample(list(zip(population, fitnesses)), k=3)
                    parent1 = max(contenders, key=lambda x: x[1])[0]
                    contenders = random.sample(list(zip(population, fitnesses)), k=3)
                    parent2 = max(contenders, key=lambda x: x[1])[0]

                    # Crossover
                    if random.random() < crossover_rate:
                        cross_pt = random.randint(1, len(param_names) - 1)
                        child = np.concatenate((parent1[:cross_pt], parent2[cross_pt:]))
                    else:
                        child = parent1.copy()

                    # Mutation
                    for i in range(len(child)):
                        if random.random() < mutation_rate:
                            low, high = bounds[i]
                            child[i] = random.uniform(low, high)

                    new_population.append(child)

                # Evaluate new population
                population = new_population
                fitnesses = [fitness(ind) for ind in population]

                # Early stopping if top fitness not improving
                if gen > 1:
                    if max(fitnesses) <= prev_best:
                        break
                prev_best = max(fitnesses)

            # Best individual
            best_idx = int(np.argmax(fitnesses))
            best_params = {name: float(val) for name, val in zip(param_names, population[best_idx])}
            strategy.update_parameters(best_params)
            final_sharpe = fitness(np.array([best_params[name] for name in param_names]))

        else:
            # Differential Evolution path
            def objective(params_arr: np.ndarray) -> float:
                return -fitness(params_arr)  # DE minimizes

            result = differential_evolution(
                objective,
                bounds=bounds,
                strategy="best1bin",
                maxiter=de_maxiter,
                popsize=de_popsize,
                mutation=(0.5, 1.0),
                recombination=0.7,
                workers=-1,
                polish=True,
            )

            best_params = {name: float(val) for name, val in zip(param_names, result.x)}
            strategy.update_parameters(best_params)
            final_sharpe = fitness(np.array([best_params[name] for name in param_names]))

        # Final performance: run multiple scenarios to produce metrics
        aggregate_metrics: Dict[str, float] = {"total_return": 0.0,
                                               "sharpe_ratio": 0.0,
                                               "max_drawdown": 0.0,
                                               "win_rate": 0.0}
        scenario_results: List[Dict[str, Any]] = []
        for _ in range(n_scenarios):
            perf = self.market_simulator.run_simulation(strategy)
            scenario_results.append(perf)
            for key in aggregate_metrics:
                aggregate_metrics[key] += perf.get(key, 0.0)
        for key in aggregate_metrics:
            aggregate_metrics[key] /= n_scenarios

        # Attach full performance dictionary, including all scenario histories
        strategy.performance = {
            "timestamp": time.time(),
            "averaged_metrics": aggregate_metrics,
            "scenario_details": scenario_results,
        }

        return strategy, aggregate_metrics["sharpe_ratio"]

    def ultra_fast_optimize_strategy(self, strategy: Strategy) -> Tuple[Strategy, float]:
        """
        ULTRA-FAST OPTIMIZATION: Optimize strategy for 30-minute completion target

        Key optimizations:
        - Reduced iterations and population size
        - Simplified fitness evaluation
        - Early stopping with convergence detection
        - Parameter caching and micro-optimization focus
        """
        import time

        if self.optimization_start_time is None:
            self.optimization_start_time = time.time()

        # Check if we're approaching time limit
        elapsed_time = time.time() - self.optimization_start_time
        if elapsed_time > self.target_completion_time * 0.9:  # 90% of target time
            print(f"⚠️ Approaching time limit ({elapsed_time/60:.1f} min) - using cached best strategy")
            return strategy, 0.5  # Return current strategy with moderate score

        config = self.ultra_fast_config
        param_ranges = self._get_strategy_param_ranges(strategy)

        if not param_ranges:
            return strategy, 0.0

        # MICRO-OPTIMIZATION: Focus only on most impactful parameters
        if config['micro_optimization']:
            # Identify top 3 most impactful parameters for this strategy type
            impact_ranking = self._get_parameter_impact_ranking(strategy)
            param_ranges = {k: v for k, v in param_ranges.items() if k in impact_ranking[:3]}

        if not param_ranges:
            return strategy, 0.0

        param_names = list(param_ranges.keys())
        bounds = [param_ranges[name] for name in param_names]

        # Ultra-fast fitness function with caching
        def ultra_fast_fitness(params: np.ndarray) -> float:
            # Create cache key
            cache_key = tuple(np.round(params, 4))  # Round for cache efficiency

            if config['cache_enabled'] and cache_key in self.performance_cache:
                return self.performance_cache[cache_key]

            temp = strategy.clone()
            param_dict = {name: float(val) for name, val in zip(param_names, params)}
            temp.update_parameters(param_dict)

            # Ultra-fast evaluation with reduced scenarios
            sharpe_vals = []
            for _ in range(config['n_scenarios']):
                try:
                    if config['fast_evaluation_mode']:
                        # Use simplified market simulation
                        perf = self.market_simulator.ultra_fast_simulation(temp)
                    else:
                        perf = self.market_simulator.run_simulation(temp)

                    sharpe = perf.get("sharpe_ratio", 0.0)
                    if not np.isnan(sharpe) and not np.isinf(sharpe):
                        sharpe_vals.append(sharpe)
                except Exception:
                    sharpe_vals.append(0.0)  # Penalty for failed simulations

            fitness_score = float(np.mean(sharpe_vals)) if sharpe_vals else 0.0

            # Cache result
            if config['cache_enabled']:
                self.performance_cache[cache_key] = fitness_score

            return fitness_score

        # Ultra-fast differential evolution
        try:
            result = differential_evolution(
                lambda x: -ultra_fast_fitness(x),  # Minimize negative fitness
                bounds,
                maxiter=config['max_iterations'],
                popsize=config['population_size'],
                workers=1,  # Single worker for simplicity
                seed=42,
                atol=config['convergence_threshold'],
                tol=config['convergence_threshold']
            )

            # Update strategy with optimized parameters
            optimized_params = {name: float(val) for name, val in zip(param_names, result.x)}
            strategy.update_parameters(optimized_params)
            final_fitness = -result.fun

        except Exception as e:
            print(f"⚠️ Ultra-fast optimization failed: {e}")
            final_fitness = 0.0

        # Quick final evaluation
        try:
            final_perf = self.market_simulator.ultra_fast_simulation(strategy)
            strategy.performance = {
                "timestamp": time.time(),
                "averaged_metrics": final_perf,
                "optimization_time": time.time() - self.optimization_start_time,
                "ultra_fast_mode": True
            }
        except Exception:
            final_fitness = 0.0

        return strategy, final_fitness

    def _get_parameter_impact_ranking(self, strategy: Strategy) -> List[str]:
        """
        MICRO-OPTIMIZATION: Identify most impactful parameters for ultra-fast optimization
        """
        strategy_type = strategy.favored_patterns[0] if strategy.favored_patterns else "trend_following"

        # Parameter impact ranking by strategy type (most impactful first)
        impact_rankings = {
            "trend_following": ["MOVING_AVERAGE_SHORT", "MOVING_AVERAGE_LONG", "risk_aversion"],
            "mean_reversion": ["RSI_PERIOD", "RSI_OVERSOLD", "RSI_OVERBOUGHT"],
            "momentum": ["MOMENTUM_PERIOD", "MOMENTUM_THRESHOLD", "risk_aversion"],
            "breakout": ["BOLLINGER_PERIOD", "BOLLINGER_STD", "VOLUME_THRESHOLD"],
            "volatility_clustering": ["VOLATILITY_WINDOW", "VOLATILITY_THRESHOLD", "risk_aversion"],
            "sentiment_analysis": ["SENTIMENT_THRESHOLD", "SENTIMENT_WINDOW", "risk_aversion"]
        }

        return impact_rankings.get(strategy_type, ["risk_aversion", "MOVING_AVERAGE_SHORT", "MOVING_AVERAGE_LONG"])

    def optimise_all_strategies(self) -> Dict[TimeFrame, List[Tuple[Strategy, float]]]:
        """Optimize every strategy in self.strategies concurrently with ultra-fast mode."""
        import time

        start_time = time.time()
        self.optimization_start_time = start_time

        print(f"🚀 ULTRA-FAST OPTIMIZATION: Starting 30-minute strategy optimization")
        print(f"⚡ Configuration: {self.ultra_fast_config['max_iterations']} iterations, "
              f"{self.ultra_fast_config['population_size']} population, "
              f"{self.ultra_fast_config['n_scenarios']} scenarios")

        optimised: Dict[TimeFrame, List[Tuple[Strategy, float]]] = {}
        total_strategies = sum(len(strategies) for strategies in self.strategies.values())
        processed_strategies = 0

        for tf in TimeFrame:
            if not self.strategies[tf]:
                optimised[tf] = []
                continue

            # Check time remaining
            elapsed_time = time.time() - start_time
            remaining_time = self.target_completion_time - elapsed_time

            if remaining_time <= 60:  # Less than 1 minute remaining
                print(f"⚠️ Time limit approaching - using current strategies for {tf}")
                optimised[tf] = [(s, 0.5) for s in self.strategies[tf].values()]
                continue

            print(f"📊 Optimizing {len(self.strategies[tf])} strategies for timeframe {tf}")

            if self.ultra_fast_mode:
                # Use ultra-fast optimization
                results = Parallel(n_jobs=self.ultra_fast_config['parallel_workers'])(
                    delayed(self.ultra_fast_optimize_strategy)(s) for s in self.strategies[tf].values()
                )
            else:
                # Use standard optimization
                results = Parallel(n_jobs=-1)(
                    delayed(self.optimize_strategy)(s) for s in self.strategies[tf].values()
                )

            optimised[tf] = sorted(results, key=lambda x: x[1], reverse=True)

            processed_strategies += len(self.strategies[tf])
            elapsed_time = time.time() - start_time

            print(f"✅ Completed {tf}: {len(results)} strategies optimized in {elapsed_time/60:.1f} minutes")
            print(f"📈 Progress: {processed_strategies}/{total_strategies} strategies "
                  f"({processed_strategies/total_strategies*100:.1f}%)")

            # Estimate remaining time
            if processed_strategies > 0:
                avg_time_per_strategy = elapsed_time / processed_strategies
                remaining_strategies = total_strategies - processed_strategies
                estimated_remaining = avg_time_per_strategy * remaining_strategies

                print(f"⏱️ Estimated remaining time: {estimated_remaining/60:.1f} minutes")

                if estimated_remaining > remaining_time:
                    print(f"⚠️ May exceed time limit - switching to faster mode")
                    self.ultra_fast_config['max_iterations'] = max(5, self.ultra_fast_config['max_iterations'] // 2)
                    self.ultra_fast_config['n_scenarios'] = 1

        total_time = time.time() - start_time
        print(f"🎉 ULTRA-FAST OPTIMIZATION COMPLETE!")
        print(f"⏱️ Total time: {total_time/60:.1f} minutes (target: 30 minutes)")
        print(f"🎯 Target achieved: {'✅ YES' if total_time <= self.target_completion_time else '❌ NO'}")
        print(f"📊 Strategies optimized: {processed_strategies}")
        print(f"💾 Cache hits: {len(self.performance_cache)}")

        return optimised

    def _get_strategy_param_ranges(self, strategy: Strategy) -> Dict[str, Tuple[float, float]]:
        """Collect just the ranges for this strategy's favored patterns."""
        combined: Dict[str, Tuple[float, float]] = {}
        for pattern in strategy.favored_patterns:
            if pattern not in VALID_STRATEGY_PARAMETERS:
                continue
            for param_name in VALID_STRATEGY_PARAMETERS[pattern]:
                if param_name in PARAMETER_RANGES:
                    combined[param_name] = PARAMETER_RANGES[param_name]
        return combined

    def _convert_params_to_dict(self, param_array: np.ndarray,
                                param_ranges: Dict[str, Tuple[float, float]]) -> Dict[str, float]:
        """Map DE array to parameter names."""
        return {name: float(val) for (name, _), val in zip(param_ranges.items(), param_array)}

    def temporary_optimise(self, strategy: Strategy) -> Generator[Strategy, None, None]:
        """Yield an optimised strategy then restore old parameters."""
        original_params = strategy.get_parameters()
        original_capital = strategy.get_capital()
        optim, _ = self.optimise_strategy(strategy)
        try:
            yield optim
        finally:
            strategy.update_parameters(original_params)
            strategy.set_capital(original_capital)

    def train_signal_generator(self, num_iterations: int = 50) -> Dict[str, Any]:
        """
        Train the signal generator after optimization to improve future performance.

        Args:
            num_iterations: Number of training iterations for the signal generator

        Returns:
            dict: Training results and metrics
        """
        try:
            # Import the signal generator training function
            import sys
            import os
            import logging

            logger = logging.getLogger(__name__)

            # Add current directory to path if needed
            if '.' not in sys.path:
                sys.path.append('.')

            # Import the training function from signal generator
            from signal_generator import train_all, run_training_loop

            logger.info(f"Starting signal generator training with {num_iterations} iterations")

            # Run the training loop
            training_results = run_training_loop(num_iterations)

            logger.info("Signal generator training completed successfully")

            return {
                "status": "success",
                "iterations_completed": len(training_results),
                "training_results": training_results
            }

        except ImportError as e:
            logger = logging.getLogger(__name__)
            logger.warning(f"Could not import signal generator: {e}")
            return {
                "status": "skipped",
                "reason": "Signal generator not available",
                "error": str(e)
            }
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"Signal generator training failed: {e}")
            return {
                "status": "failed",
                "error": str(e)
            }

    def optimize_with_signal_training(self, strategy: Strategy) -> Tuple[Strategy, float]:
        """
        Optimize a strategy and then train the signal generator for improved future performance.

        Args:
            strategy: Strategy to optimize

        Returns:
            Tuple[Strategy, float]: Optimized strategy and its performance score
        """
        import logging
        logger = logging.getLogger(__name__)

        # First, perform standard optimization
        logger.info(f"Starting optimization for strategy: {strategy.name}")
        optimized_strategy, performance_score = self.optimize_strategy(strategy)

        # Then train the signal generator
        logger.info("Starting signal generator training phase")
        signal_training_iterations = 25  # Reasonable number for post-optimization training

        signal_results = self.train_signal_generator(signal_training_iterations)

        # Log the results
        if signal_results["status"] == "success":
            logger.info(f"Signal generator training completed: {signal_results['iterations_completed']} iterations")
        else:
            logger.warning(f"Signal generator training {signal_results['status']}: {signal_results.get('reason', 'Unknown')}")

        return optimized_strategy, performance_score
