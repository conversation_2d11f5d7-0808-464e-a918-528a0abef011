#!/usr/bin/env python3
"""
Test script for the strategy system without external dependencies
"""
import json
import pandas as pd
import numpy as np
from typing import Dict, List, Any
from enum import Enum

# Define TimeFrame locally to avoid import issues
class TimeFrame(Enum):
    SHORT_TERM = 'short_term'
    MID_TERM = "mid_term"
    LONG_TERM = "long_term"
    SEASONAL_TERM = "seasonal_term"

# Import parameter config directly
try:
    from parameter_config import VALID_STRATEGY_PARAMETERS
except ImportError:
    print("⚠️  Could not import parameter_config, using minimal config")
    VALID_STRATEGY_PARAMETERS = {
        'trend_following': {
            'market_parameters': {
                'signal_strengths': {},
                'directional_biases': {},
                'action_sensitivities': {}
            },
            'calculation_parameters': {}
        }
    }

def create_mock_market_data():
    """Create mock market data for testing"""
    dates = pd.date_range('2023-01-01', periods=1000, freq='h')
    np.random.seed(42)
    
    # Generate realistic price data
    price = 50000  # Starting BTC price
    prices = [price]
    
    for i in range(999):
        change = np.random.normal(0, 0.02)  # 2% volatility
        price = price * (1 + change)
        prices.append(price)
    
    data = pd.DataFrame({
        'timestamp': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'price': prices,  # Add price column for strategy compatibility
        'volume': np.random.uniform(100, 1000, 1000)
    })
    
    data.set_index('timestamp', inplace=True)
    return data

def test_strategy_factory():
    """Test the strategy factory functionality"""
    print("Testing Strategy Factory...")

    # Test loading existing strategies directly from JSON
    try:
        with open('strategies.json', 'r') as f:
            existing_strategies = json.load(f)
        print(f"✅ Loaded {len(existing_strategies)} existing strategies")

        # Test strategy structure
        if existing_strategies:
            first_key = list(existing_strategies.keys())[0]
            first_strategy = existing_strategies[first_key]

            required_fields = ['name', 'description', 'patterns', 'parameters', 'timeframe', 'market_conditions']
            for field in required_fields:
                if field not in first_strategy:
                    print(f"❌ Missing required field: {field}")
                    return False

            print(f"✅ Strategy structure is valid")
            print(f"   - Name: {first_strategy['name']}")
            print(f"   - Description: {first_strategy['description'][:50]}...")
            print(f"   - Timeframe: {first_strategy['timeframe']}")
            print(f"   - Patterns: {first_strategy['patterns']}")

            # Test parameter structure
            params = first_strategy['parameters']
            if 'market_parameters' in params and 'calculation_parameters' in params:
                print(f"✅ Parameters have correct nested structure")
            else:
                print(f"⚠️  Parameters may have flat structure")

    except Exception as e:
        print(f"❌ Failed to load strategies: {e}")
        return False

    return True

def test_parameter_validation():
    """Test parameter validation"""
    print("\nTesting Parameter Validation...")
    
    # Test valid strategy parameters
    for strategy_type, config in VALID_STRATEGY_PARAMETERS.items():
        print(f"✅ Strategy type '{strategy_type}' has valid config")
        
        # Check structure
        if 'market_parameters' not in config:
            print(f"❌ Missing market_parameters in {strategy_type}")
            return False
            
        if 'calculation_parameters' not in config:
            print(f"❌ Missing calculation_parameters in {strategy_type}")
            return False
            
        # Check market parameter subgroups
        market_params = config['market_parameters']
        required_subgroups = ['signal_strengths', 'directional_biases', 'action_sensitivities']
        
        for subgroup in required_subgroups:
            if subgroup not in market_params:
                print(f"⚠️  Missing {subgroup} in {strategy_type} market_parameters")
    
    print("✅ Parameter validation completed")
    return True

def test_strategy_creation():
    """Test creating a new strategy manually"""
    print("\nTesting Manual Strategy Creation...")

    try:
        # Create a simple test strategy structure
        test_strategy = {
            "name": "Test Strategy",
            "description": "A test strategy for validation",
            "parameters": {
                "market_parameters": {
                    "signal_strengths": {
                        "trend_strength_score": 0.5
                    },
                    "directional_biases": {},
                    "action_sensitivities": {
                        "risk_aversion": 0.3
                    }
                },
                "calculation_parameters": {
                    "MOVING_AVERAGE_SHORT": 10,
                    "MOVING_AVERAGE_LONG": 20
                }
            },
            "patterns": ["trend_following"],
            "timeframe": "short_term",
            "market_conditions": {
                "optimal_volatility": "0.01-0.05",
                "optimal_trend": "bullish",
                "optimal_liquidity": "1.2+"
            }
        }

        print(f"✅ Created test strategy: {test_strategy['name']}")
        print(f"✅ Strategy structure is valid")

        return True

    except Exception as e:
        print(f"❌ Failed to create test strategy: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Strategy System Tests\n")
    
    tests = [
        test_parameter_validation,
        test_strategy_factory,
        test_strategy_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ Test {test.__name__} failed")
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The strategy system is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
