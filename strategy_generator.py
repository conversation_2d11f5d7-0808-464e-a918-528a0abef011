import google.generativeai as genai
import pandas as pd
import numpy as np
import os
from strategy_factory import StrategyFactory
import json
from decimal import Decimal
from functools import lru_cache
from typing import Dict, List, Optional, Union, Any, Tuple
from datetime import datetime
from dataclasses import dataclass
from enum import Enum
from strategy import (
    Strategy,
    TimeFrame,
    TrendFollowingStrategy,
    MeanReversionStrategy,
    MomentumStrategy,
    VolatilityStrategy,
    StatisticalArbitrageStrategy,
    SentimentAnalysisStrategy
)
from parameter_config import VALID_STRATEGY_PARAMETERS
import logging
import asyncio
import time
import tiktoken
import re
from config import Config

# Enhanced logging configuration
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('strategy_generator.log'),
        logging.StreamHandler()
    ]
) 

@dataclass
class MarketMetrics:
    """Container for calculated market metrics"""
    rsi: float
    volatility: float
    trend_strength: float
    volume_profile: float
    moving_averages: Dict[str, float]
    support_resistance: Dict[str, float]

class StrategyValidationError(Exception):
    """Custom exception for strategy validation errors"""
    pass

class MarketState(Enum):
    TRENDING = "TRENDING"
    RANGING = "RANGING"
    VOLATILE = "VOLATILE"
    CONSOLIDATING = "CONSOLIDATING"

class StrategyGenerator:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.model = None
        self._setup_logging()
        self.strategy_cache = {}
        self.market_metrics_cache = {}
        genai.configure(api_key=os.environ['GOOGLE_AI_API_KEY'])
        self.strategy_factory = StrategyFactory()

        try:
            api_key = os.environ.get('GOOGLE_AI_API_KEY')
            if not api_key:
                raise ValueError("GOOGLE_AI_API_KEY not found")
            genai.configure(api_key=api_key)
            self.model = genai.GenerativeModel('models/gemini-1.5-pro')
        except Exception as e:
            self.logger.error(f"[AI INIT] Gemini model init failed: {e}")
            self.model = None  # fail safely

    def _initialize_ai(self):
        """Initialize AI model with error handling"""
        try:
            api_key = os.environ.get('GOOGLE_AI_API_KEY')
            if not api_key:
                raise ValueError("GOOGLE_AI_API_KEY not found in environment variables")
            genai.configure(api_key=api_key)
            self.model = genai.GenerativeModel('models/gemini-1.5-pro')
        except Exception as e:
            self.logger.error(f"AI initialization failed: {e}")

    def _build_param_ranges(self) -> Dict[str, Tuple[float, float]]:
        ranges = {}
        for strat in VALID_STRATEGY_PARAMETERS.values():
            # Calculation parameters
            for p, bounds in strat['calculation_parameters'].items():
                ranges[p] = bounds
            # Market parameters (all three subgroups)
            for cat in strat['market_parameters'].values():
                for p, bounds in cat.items():
                    ranges[p] = bounds
        return ranges

    
    def count_tokens(text: str, model_name="gpt-3.5-turbo") -> int:
        enc = tiktoken.encoding_for_model(model_name)
        return len(enc.encode(text))
        
    def _setup_logging(self):
        """Configure detailed logging"""
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.DEBUG)

    def calculate_market_metrics(self, data: pd.DataFrame) -> MarketMetrics:
        """Calculate comprehensive market metrics"""
        try:
            return MarketMetrics(
                rsi=self._calculate_rsi(data['close']),
                volatility=self._calculate_volatility(data),
                trend_strength=self._calculate_trend_strength(data),
                volume_profile=self._analyze_volume_profile(data),
                moving_averages=self._calculate_moving_averages(data),
                support_resistance=self._find_support_resistance(data)
            )
        except Exception as e:
            self.logger.error(f"Market metrics calculation failed: {e}")
            raise

    def _create_enhanced_prompt(self, market_data: pd.DataFrame) -> str:
        timeframes = {
            "Short-Term": market_data.tail(72),        # 3 days of 1h data
            "Mid-Term": market_data.tail(504),         # 3 weeks
            "Long-Term": market_data.tail(4320),       # 6 months
            "Seasonal-Term": market_data               # assume ~4 years full
        }


        prompt_sections = []

        for label, data in timeframes.items():
            metrics = self.calculate_market_metrics(data)
            trend_duration = self.calculate_trend_duration(data)
            candle_spread_ratio = self.calculate_candle_spread_ratio(data)
            rolling_return = self.calculate_rolling_return(data)
            market_eff = self.calculate_market_efficiency_ratio(data)
            liquidity = self.calculate_liquidity_score(data)

            support_resistance_raw = self.identify_support_resistance_levels(data)
            summarized_support = self.summarize_support_resistance_levels(
                support_resistance_raw.get("support_levels", [])
            )
            summarized_resistance = self.summarize_support_resistance_levels(
                support_resistance_raw.get("resistance_levels", [])
            )

            section = f"""
{label} Market Summary:
- RSI: {metrics.rsi:.2f}
- Volatility: {metrics.volatility:.4f}
- Trend Strength: {metrics.trend_strength:.2f}
- Candle Spread Ratio: {candle_spread_ratio:.4f}
- Rolling Return (24h): {rolling_return:.4f}
- Trend Duration: {trend_duration} hours
- Market Efficiency: {market_eff:.4f}
- Liquidity Score: {liquidity:.4f}
- Support Levels: {summarized_support}
- Resistance Levels: {summarized_resistance}
"""
            if label in ["Short-Term", "Mid-Term"]:
                volume_profile = self.analyze_volume_profile(data)
                section += f"- Volume Profile: {volume_profile}\n"

            prompt_sections.append(section.strip())

        # --- Valid Patterns and Parameters Section ---
        def format_params(params_dict: Dict[str, List[str]]) -> str:
            return "\n".join([f"- {pattern}: {', '.join(params)}" for pattern, params in params_dict.items()])

        def format_ranges(ranges_dict: Dict[str, Tuple[float, float]]) -> str:
            return "\n".join([f"- {param}: {bounds[0]} to {bounds[1]}" for param, bounds in ranges_dict.items()])

        valid_patterns_text = format_params(VALID_STRATEGY_PARAMETERS)
        flat_ranges = self._build_param_ranges()
        param_ranges_text = format_ranges(flat_ranges)
        existing_descriptions = self.strategy_factory.get_existing_strategy_descriptions()

        validation_section = f"""
    Valid Strategy Patterns and Their Parameters:
    {valid_patterns_text}

    Parameter Ranges:
    The stragies' parameter should stay inside these ranges.
    {param_ranges_text}
    """.strip()
        
        strategy_guide = """
Strategy Pattern Guide (When to Use):

- trend_following:
  Use when price shows clear directional momentum over time (e.g., rising highs/lows or falling lows/highs).
  ❗ Best for bull or bear trends with consistency — avoid in sideways or whipsaw markets.

- mean_reversion:
  Use when price oscillates around a stable average or frequently "snaps back" after deviation.
  ❗ Best for ranging markets with defined upper/lower bounds — avoid in trending phases.

- momentum:
  Use when price has recently broken out or is accelerating strongly in a direction.
  ❗ Best for early trend detection or short bursts — avoid if market lacks volume or commitment.

- breakout:
  Use when price is compressing near key support/resistance and building potential energy.
  ❗ Best for low-volatility, high-tension phases — avoid in already volatile or broken-out markets.

- volatility_clustering:
  Use when the market shifts between quiet and chaotic periods, e.g., quiet buildup then explosion.
  ❗ Best for adaptive strategies that react to changing noise levels — avoid in flat or monotonic markets.

- statistical_arbitrage:
  Use when two or more assets are strongly cointegrated or correlated, and one deviates.
  ❗ Best for multi-asset setups with stable relationships — avoid in decoupling or irrational markets.

- sentiment_analysis:
  Use when social, news, or on-chain sentiment shifts before price reacts.
  ❗ Best for high-attention or hype-driven phases — avoid in quiet, purely technical environments.
""".strip()
        
        parameter_descriptions = """
These are detailed explanations for each parameter, including what specific values represent.

# Market Parameters

## Signal Strengths
- trend_strength_score: (0.0–1.0) 0 means no trend required; 1 means only very strong trends qualify.
- breakout_strength_multi: (0.1–5.0) Low values focus on small breakouts; high values require major price+volume+volatility breakouts.
- momentum_persistence_bias: (0.0–1.0) 0 ignores sustained momentum; 1 only acts on long-running momentum.
- reversion_intensity_score: (0.0–1.0) 0 ignores mean reversion; 1 expects immediate strong reversion.
- volume_mean_reversion_weight: (0.0–1.0) 0 ignores volume spikes; 1 gives full weight to unusual volume in reversion signals.
- mean_bias: (-1.0–1.0) -1 only trades oversold reverts; +1 only trades overbought reverts; 0 is neutral.
- momentum_score: (0.0–1.0) 0 ignores acceleration; 1 only trades the fastest moves.
- volume_momentum_weight: (0.0–1.0) 0 uses price-only; 1 uses volume spikes equally with price for momentum.
- time_of_day_breakout_weight: (0.0–1.0) 0 ignores time-of-day; 1 fully biases known high-breakout hours.
- volatility_extremes_weight: (0.0–1.0) 0 ignores extreme vol; 1 only acts on tail events.
- volatility_autocorr_strength: (0.0–1.0) 0 ignores past vol; 1 treats volatility as fully auto-correlated.
- sentiment_momentum_score: (-1.0–1.0) -1 only reacts to worsening sentiment; +1 only to improving sentiment; 0 neutral.
- sentiment_volatility_interaction: (0.0–1.0) 0 treats sentiment independent; 1 scales sentiment by current volatility.

## Directional Biases
- breakout_direction_confidence: (0.0–1.0) 0 means random direction; 1 means only trade when direction is crystal clear.
- RSI_directional_bias / MACD_directional_bias: (-1.0–1.0) -1 only oversold longs; +1 only overbought shorts; 0 neutral.
- volatility_regime_bias: (-1.0–1.0) -1 only low-vol; +1 only high-vol; 0 accepts both.
- event_driven_bias: (-1.0–1.0) -1 only negative news; +1 only positive news; 0 ignores news.

## Action Sensitivities
- trailing_stop_sensitivity: (0.001–0.3) Lower values trail tightly; higher values give more room before stopping.
- risk_aversion: (0.0–1.0) 0 takes max risk; 1 minimizes position size under uncertainty.
- position_sizing_aggressiveness: (0.1–2.0) 0.1 small positions; 2 large positions.
- drawdown_recovery_sensitivity: (0.0–1.0) 0 waits fully to recover; 1 re-enters immediately after drawdown.
- time_of_day_sensitivity: (0.0–1.0) 0 ignores time; 1 only trades in peak hours.
- entry_threshold_sensitivity / exit_threshold_sensitivity: (0.01–0.5) Lower values react early; higher values require larger deviation to trigger.
- noise_filter_sensitivity: (0.0–1.0) 0 no filter; 1 filters almost all small fluctuations.
- false_breakout_filter_sensitivity: (0.0–1.0) 0 accepts all breakouts; 1 strictly filters false ones.
- post_breakout_retest_sensitivity: (0.0–1.0) 0 no retest; 1 requires full retest before entry.

# Calculation Parameters
- MOVING_AVERAGE_SHORT / MOVING_AVERAGE_LONG: integer periods, e.g. 5–500 bars, controlling MA responsiveness.
- TREND_CONFIRMATION_PERIOD / trend_strength_window: 1–100 bars to fit trend slope line.
- BREAKOUT_PERIOD / breakout_period: lookback length (5–500 bars) to define high/low breakout levels.
- volume_ma_period: 5–50 bars for smoothing volume values.
- atr_period: 5–100 bars for volatility range measurement.
- MEAN_WINDOW / std_window: 2–500 bars for calculating mean and σ in reversion.
- BOLLINGER_PERIOD / bollinger_std_dev: 5–500 bars and 0.5–5 σ for band width.
- time_decay_rate: 0.90–0.99 exp. decay constant for older signals.
- MOMENTUM_PERIOD: 1–200 bars for rate-of-change calculation.
- RSI_PERIOD / RSI_OVERBOUGHT / RSI_OVERSOLD: 2–100 bars and threshold levels (10–90) for strength.
- MACD_FAST / MACD_SLOW / MACD_SIGNAL: 5–50 & 10–200 periods for EMAs and signal smoothing.
- noise_filter_window: 5–50 bars to compute noise threshold.
- GARCH_LAG: 1–20 lags in volatility prediction.
- HIGH_VOLATILITY_THRESHOLD / LOW_VOLATILITY_THRESHOLD: 0.1–5 & 0.01–1 to classify regimes.
- volatility_autocorr_lag: 1–20 bars to measure vol autocorrelation.
- volatility_extremes_window: 5–100 bars to detect volatility outliers.
- SENTIMENT_WINDOW: 1–100 data points to average sentiment.
- NEWS_IMPACT_DECAY / SENTIMENT_SMOOTHING_FACTOR: 0.1–1.0 for weighting old vs new sentiment.
- SENTIMENT_VOLUME_THRESHOLD: 0.5–5.0 number of articles to validate sentiment.
- SENTIMENT_MOMENTUM_PERIOD: 1–100 bars for sentiment change detection.
""".strip()

        # --- Final AI Instructions ---
        instruction = (
            "You are to generate exactly **2 unique trading strategies** for BTC trading:\n"
            "- One momentum-based strategy\n"
            "- One mean-reversion strategy\n"
            "Keep in mind you are creating strategies for BTC trading.\n\n"
            "Rules:\n"
            "1. Each strategy must use only the patterns and parameters listed above.\n"
            "2. Each strategy must only use the parameters within its type. Ex: A trend following strategy can only use parameters from the trend following type.\n"
            "3. Each strategy must use parameters within the defined ranges.\n"
            "4. Keep repetition of logic or parameters across strategies to a minimum.\n"
            "5. Use technical indicators relevant to each timeframe length.\n"
            "6. Be original and adapt the logic to market conditions.\n"
            "7. Each strategy must only have one pattern.\n"
            "8. You must show all strategies created.\n"
            "9. Do not return anything but the strategies in the json format.\n\n"
            "Each strategy must include a \"market_conditions\" field with:\n"
            "- optimal_volatility: decimal range string, e.g., \"0.05-0.30\"\n"
            "- optimal_trend: \"bullish\" or \"bearish\", or \"range\"\n"
            "- optimal_liquidity: minimum threshold, formatted as \"\u22651.2\" or \"1.2+\"\n\n"
            "Each strategy must include a \"parameters\" field with nested subgroups:\n"
            "- market_parameters:\n"
            "  - signal_strengths\n"
            "  - directional_biases\n"
            "  - action_sensitivities\n"
            "- calculation_parameters\n"
            "⚠️ Do not flatten the parameters. Keep them grouped as shown in the JSON example.\n"
            "⚠️ Every parameter must be numeric. No strings, no nulls.\n\n"
            "Output format (raw JSON):\n"
            "[\n"
            "  {\n"
            "    \"name\": \"Momentum Surge Tracker\",\n"
            "    \"description\": \"Capitalizes on strong directional moves confirmed by volume. Reacts quickly to fast moves and uses volume-weighted signals.\",\n"
            "    \"patterns\": [\"momentum\"],\n"
            "    \"parameters\": {\n"
            "      \"market_parameters\": {\n"
            "        \"signal_strengths\": {\n"
            "          \"momentum_score\": 0.83,\n"
            "          \"volume_momentum_weight\": 0.61,\n"
            "          \"momentum_persistence_bias\": 0.72\n"
            "        },\n"
            "        \"directional_biases\": {\n"
            "          \"RSI_directional_bias\": -0.56,\n"
            "          \"MACD_directional_bias\": 0.44,\n"
            "          \"volatility_regime_bias\": 0.39\n"
            "        },\n"
            "        \"action_sensitivities\": {\n"
            "          \"noise_filter_sensitivity\": 0.25,\n"
            "          \"risk_aversion\": 0.32,\n"
            "          \"position_sizing_aggressiveness\": 1.45,\n"
            "          \"drawdown_recovery_sensitivity\": 0.61\n"
            "        }\n"
            "      },\n"
            "      \"calculation_parameters\": {\n"
            "        \"MOMENTUM_PERIOD\": 45,\n"
            "        \"RSI_PERIOD\": 14,\n"
            "        \"RSI_OVERBOUGHT\": 80,\n"
            "        \"RSI_OVERSOLD\": 25,\n"
            "        \"MACD_FAST\": 12,\n"
            "        \"MACD_SLOW\": 26,\n"
            "        \"MACD_SIGNAL\": 9,\n"
            "        \"volume_ma_period\": 20,\n"
            "        \"noise_filter_window\": 14\n"
            "      }\n"
            "    },\n"
            "    \"timeframe\": \"short_term\",\n"
            "    \"market_conditions\": {\n"
            "      \"optimal_volatility\": \"0.10-0.40\",\n"
            "      \"optimal_trend\": \"bullish\",\n"
            "      \"optimal_liquidity\": \"\u22651.5\"\n"
            "    }\n"
            "  }\n"
            "]\n\n"
            f"Existing strategies:\n{existing_descriptions}\n\n"
        )

        base_prompt = "\n\n".join(prompt_sections)
        return f"{base_prompt}\n\n{validation_section}\n\n{strategy_guide}\n\n{parameter_descriptions}\n\n{instruction}"

    async def generate_strategies(self, market_data: pd.DataFrame) -> Dict[TimeFrame, List[Strategy]]:
        try:
            market_data_key = self._create_hashable_key(market_data)

            # Cache check
            if market_data_key in self.strategy_cache:
                self.logger.info("Using cached strategies")
                return self.strategy_cache[market_data_key]

            strategies = {timeframe: [] for timeframe in TimeFrame}
            prompt = self._create_enhanced_prompt(market_data)

            self.logger.debug(f"[Prompt Size Check]\n{prompt}")

            response = self.model.generate_content(prompt)
            raw = response.text if hasattr(response, 'text') else str(response)
            cleaned = self.clean_ai_json(raw)

            self.logger.debug(f"[Raw Gemini Response]\n{raw}")
            self.logger.debug(f"[Sanitized JSON Attempt]\n{cleaned}")

            try:
                strategy_data_list = json.loads(cleaned)
                if not isinstance(strategy_data_list, list):
                    raise ValueError("AI response is not a list of strategy dicts.")

            except Exception as e:
                self.logger.error(f"JSON parsing failed: {e}")
                # Prevent undefined variable and stop processing
                return self._get_fallback_strategies()

            timeframes = list(TimeFrame)
            for i, strategy_spec in enumerate(strategy_data_list):
                current_timeframe = timeframes[i % len(timeframes)]

                try:
                    if self._validate_strategy(strategy_spec):
                        market_cond = strategy_spec.get('market_conditions', {})

                        # --- Parse optimal_volatility: "0.05-0.30"
                        vol_range_str = market_cond.get('optimal_volatility', '0.0-1.0')
                        try:
                            vol_min, vol_max = map(float, vol_range_str.split('-'))
                            optimal_volatility = (vol_min, vol_max)
                        except Exception as e:
                            self.logger.warning(f"[Volatility Parse] Invalid format '{vol_range_str}': {e}")
                            optimal_volatility = (0.0, 1.0)

                        # --- Parse optimal_trend: 'bullish' | 'bearish' | 'range'
                        trend = market_cond.get('optimal_trend', 'range').lower()
                        if trend not in ['bullish', 'bearish', 'range']:
                            self.logger.warning(f"[Trend Parse] Unknown trend value '{trend}', defaulting to 'range'")
                            trend = 'range'

                        # --- Parse optimal_liquidity: "≥1.2" or "1.2+"
                        liq_raw = market_cond.get('optimal_liquidity', '1.0+')
                        try:
                            liq_value = float(re.sub(r'[^\d.]', '', liq_raw))  # strip ≥ or +
                        except ValueError:
                            self.logger.warning(f"[Liquidity Parse] Could not parse liquidity value '{liq_raw}', defaulting to 1.0")
                            liq_value = 1.0

                        # --- Parse nested parameters into flat dict ---


                        params_raw = strategy_spec.get('parameters', {})


                        if isinstance(params_raw, dict) and 'market_parameters' in params_raw:


                            calc = params_raw.get('calculation_parameters', {})


                            mp_block = params_raw.get('market_parameters', {})


                            flat_params = {**calc}


                            for cat in ['signal_strengths', 'directional_biases', 'action_sensitivities']:


                                flat_params.update(mp_block.get(cat, {}))


                        else:


                            flat_params = params_raw


                        # replace nested parameters with flat dict when constructing Strategy


                        strategy = Strategy(
                            name=strategy_spec['name'],
                            description=strategy_spec['description'],
                            parameters=flat_params,  # use flattened nested parameters
                            favored_patterns=(strategy_spec['patterns'] if isinstance(strategy_spec['patterns'], list)
                                              else [strategy_spec['patterns']]),
                            time_frame=current_timeframe,
                            optimal_volatility=optimal_volatility,
                            optimal_trend=trend,
                            optimal_liquidity=liq_value
                        )

                        strategies[current_timeframe].append(strategy)

                        strategy_key = f"{strategy.name}_{current_timeframe}_{hash(frozenset(strategy.parameters.items()))}"
                        await self.strategy_factory.update_strategy(strategy_key, strategy)

                        self.logger.info(f"[Strategy OK] {current_timeframe}: {strategy.name}")
                    else:
                        raise ValueError("Validation failed")

                except Exception as e:
                    self.logger.warning(f"[Strategy Error] {current_timeframe}: {e}")

            # Cache and save
            self.strategy_cache[market_data_key] = strategies

            return strategies

        except asyncio.TimeoutError:
            self.logger.error("API call timed out")
            return self._get_fallback_strategies()

        except Exception as e:
            self.logger.error(f"Unknown strategy generation failure: {e}", exc_info=True)
            return self._get_fallback_strategies()

    def _create_hashable_key(self, df: pd.DataFrame) -> str:
        """Create a hashable key from DataFrame for caching purposes"""
        # Use the last few rows and key statistics as the cache key
        try:
            last_rows = df.tail(5)
            stats = {
                'mean': float(df['close'].mean()),
                'std': float(df['close'].std()),
                'last': float(df['close'].iloc[-1]),
                'timestamp': str(df.index[-1])
            }
            return f"{hash(tuple(last_rows['close']))}-{hash(frozenset(stats.items()))}"
        except Exception as e:
            self.logger.error(f"Error creating hashable key: {str(e)}")
            return str(hash(time.time()))  # Fallback to timestamp hash

    def _validate_strategy(self, strategy_data: Dict) -> bool:
        """
        Validate strategy structure, flatten parameters, check ranges from VALID_STRATEGY_PARAMETERS, and market_conditions formats.
        """
        try:
            # 1) Required top-level keys
            required = ['name', 'description', 'parameters', 'patterns', 'timeframe', 'market_conditions']
            for key in required:
                if key not in strategy_data:
                    self.logger.warning(f"Missing key in strategy: {key}")
                    return False

            # 2) Flatten nested parameters
            raw = strategy_data['parameters']
            if isinstance(raw, dict) and 'market_parameters' in raw:
                flat = {}
                flat.update(raw.get('calculation_parameters', {}))
                mp = raw['market_parameters']
                for cat in ['signal_strengths', 'directional_biases', 'action_sensitivities']:
                    flat.update(mp.get(cat, {}))
            else:
                flat = raw

            # 3) Build range map from VALID_STRATEGY_PARAMETERS
            range_map = {}
            for cfg in VALID_STRATEGY_PARAMETERS.values():
                range_map.update(cfg['calculation_parameters'])
                for grp in cfg['market_parameters'].values():
                    range_map.update(grp)

            # 4) Validate each flat parameter
            for p, v in flat.items():
                if p in range_map:
                    min_v, max_v = range_map[p]
                    try:
                        val = float(v)
                    except:
                        self.logger.warning(f"Parameter '{p}' should be numeric but got: {v}")
                        return False
                    if not (min_v <= val <= max_v):
                        self.logger.warning(f"Param '{p}' out of bounds: {val}")
                        return False

            # 5) Validate patterns
            pats = strategy_data['patterns']
            pats = [pats] if isinstance(pats, str) else pats
            for pat in pats:
                if pat not in VALID_STRATEGY_PARAMETERS:
                    self.logger.warning(f"Unknown pattern: {pat}")
                    return False

            # 6) Validate market_conditions
            mc = strategy_data['market_conditions']
            vol = mc.get('optimal_volatility', '')
            if not re.match(r"^\d+\.?\d*-\d+\.?\d*$", vol):
                return False
            tr = mc.get('optimal_trend', '').lower()
            if tr not in ['bullish', 'bearish', 'range']:
                return False
            liq = mc.get('optimal_liquidity', '')
            if not re.match(r"^≥?\d+\.?\d*\+?$", liq):
                return False

            return True
        except Exception as e:
            self.logger.error(f"Strategy validation crash: {e}")
            return False

    def _calculate_volatility(self, data: pd.DataFrame) -> float:
        return data['close'].pct_change().std() * np.sqrt(252)

    def _calculate_trend_strength(self, data: pd.DataFrame) -> float:
        return abs(data['close'].pct_change().mean()) * 100

    def _analyze_volume_profile(self, data: pd.DataFrame) -> float:
        return (data['volume'] * data['close']).mean()

    def _calculate_moving_averages(self, data: pd.DataFrame) -> Dict[str, float]:
        return {
            'sma_20': data['close'].rolling(20).mean().iloc[-1],
            'sma_50': data['close'].rolling(50).mean().iloc[-1],
            'sma_200': data['close'].rolling(200).mean().iloc[-1]
        }

    def _find_support_resistance(self, data: pd.DataFrame) -> Dict[str, float]:
        return {
            'support': data['low'].tail(20).min(),
            'resistance': data['high'].tail(20).max()
        }

    def _determine_market_state(self, metrics: MarketMetrics) -> MarketState:
        if metrics.trend_strength > 0.5:
            return MarketState.TRENDING
        elif metrics.volatility > 0.2:
            return MarketState.VOLATILE
        elif metrics.volatility < 0.1:
            return MarketState.CONSOLIDATING
        return MarketState.RANGING

    def _validate_config(self):
        required_keys = ['API_KEY', 'ADAPTIVE_PARAMS', 'TIMEFRAMES']
        if not all(key in self.config for key in required_keys):
            raise ValueError(f"Missing required config keys: {required_keys}")

    def _resample_data(self, data: pd.DataFrame, timeframe: TimeFrame) -> pd.DataFrame:
        resample_rules = {
            TimeFrame.SHORT_TERM: 'h',
            TimeFrame.MID_TERM: 'D',
            TimeFrame.LONG_TERM: 'W',
            TimeFrame.SEASONAL_TERM: 'ME'
        }
    
        return data.resample(resample_rules[timeframe]).agg({
            'open': 'first',
            'high': 'max',
            'low': 'min',
            'close': 'last',
            'volume': 'sum'
        }).dropna()

    def _get_cached_strategies(self, timeframe: TimeFrame) -> List[Strategy]:
        if timeframe in self.strategy_cache:
            if time.time() - self.strategy_cache[timeframe]['timestamp'] < 3600:  # 1 hour cache
                return self.strategy_cache[timeframe]['strategies']
        return []

    def _cache_strategies(self, timeframe: TimeFrame, strategies: List[Strategy]) -> None:
        self.strategy_cache[timeframe] = {
            'timestamp': time.time(),
            'strategies': strategies
        }

    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> float:
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi.iloc[-1]
    
    def analyze_volume_profile(self, market_data: pd.DataFrame) -> Dict[str, float]:
        volume = market_data['volume']
        price = market_data['close']
    
        # Calculate volume-weighted metrics
        vwap = (price * volume).cumsum() / volume.cumsum()
        volume_ma = volume.rolling(window=20).mean()
        relative_volume = volume / volume_ma
    
        # Identify key volume levels
        high_volume_threshold = volume_ma.mean() * 1.5
        low_volume_threshold = volume_ma.mean() * 0.5
    
        return {
            'vwap': vwap.iloc[-1],
            'relative_volume': relative_volume.iloc[-1],
            'high_volume_zones': (volume > high_volume_threshold).sum() / len(volume),
            'low_volume_zones': (volume < low_volume_threshold).sum() / len(volume),
            'volume_trend': (volume_ma.iloc[-1] / volume_ma.iloc[0]) - 1
        }

    def identify_support_resistance_levels(self, market_data: pd.DataFrame) -> Dict[str, List[float]]:
        prices = market_data['close']
        highs = market_data['high']
        lows = market_data['low']
        volumes = market_data['volume']
    
        # Calculate price clusters
        price_clusters = pd.concat([highs, lows])
        hist, bins = np.histogram(price_clusters, bins=50)
    
        # Find support and resistance levels
        support_levels = []
        resistance_levels = []
        for idx in range(1, len(prices)-1):
            if (lows.iloc[idx] < lows.iloc[idx-1] and lows.iloc[idx] < lows.iloc[idx+1]):
                support_levels.append(lows.iloc[idx])
            if (highs.iloc[idx] > highs.iloc[idx-1] and highs.iloc[idx] > highs.iloc[idx+1]):
                resistance_levels.append(highs.iloc[idx])
    
        return {
            'support_levels': support_levels,
            'resistance_levels': resistance_levels
        }

    def calculate_market_efficiency_ratio(self, market_data: pd.DataFrame) -> float:
        price = market_data['close']
        directional_movement = abs(price.iloc[-1] - price.iloc[0])
        path_movement = abs(price.diff()).sum()
        return directional_movement / path_movement if path_movement != 0 else 0

    def calculate_liquidity_score(self, market_data: pd.DataFrame) -> float:
        volume_ma = market_data['volume'].rolling(20).mean()
        spread = (market_data['high'] - market_data['low']) / market_data['close']
        return (volume_ma.iloc[-1] / volume_ma.mean()) * (1 - spread.mean())

    def summarize_support_resistance_levels(self, levels: List[float], max_levels: int = 5) -> List[float]:
        """Round and reduce number of levels to avoid overwhelming prompt."""
        return sorted(set(round(val, 4) for val in levels))[:max_levels]

    def calculate_candle_spread_ratio(self, data: pd.DataFrame) -> float:
        """Compute the average candle spread (high-low) relative to the close."""
        spreads = data['high'] - data['low']
        return float((spreads / data['close']).mean())

    def calculate_rolling_return(self, data: pd.DataFrame, period: int = 24) -> float:
        """Calculate rolling return over a period of hours."""
        returns = data['close'].pct_change(periods=period)
        return returns.iloc[-1] if not returns.empty else 0.0


    def calculate_trend_duration(self, data: pd.DataFrame) -> int:
        """Estimate trend duration in hours based on consecutive increasing or decreasing closes."""
        closes = data['close']
        trend = np.sign(closes.diff())
        longest_streak = current_streak = 1
        last_direction = trend.iloc[-1]

        for i in range(len(trend) - 2, -1, -1):
            if trend.iloc[i] == last_direction and trend.iloc[i] != 0:
                current_streak += 1
            else:
                break
        return current_streak
    
    @staticmethod
    def clean_ai_json(raw_text: str) -> str:
        """
        Cleans common AI output issues: markdown, trailing commas, leading fluff.
        """
        try:
            # Remove code block markers (```json ... ```)
            cleaned = re.sub(r"```(json)?", "", raw_text.strip())

            # Remove trailing commas before } or ]
            cleaned = re.sub(r',\s*(\}|\])', r'\1', cleaned)

            # Clip everything before first [
            start = cleaned.find('[')
            if start != -1:
                cleaned = cleaned[start:]

            return cleaned
        except Exception as e:
            return raw_text  # fallback

    def _get_fallback_strategies(self) -> Dict[TimeFrame, List[Strategy]]:
        """Return empty strategies dict as fallback when generation fails"""
        return {timeframe: [] for timeframe in TimeFrame}

if __name__ == "__main__":
    config = Config()
    strategy_types = ["trend_following", "breakout", "mean_reversion", "momentum", "volatility_clustering", "sentiment_analysis"]
    strategy_type_index = 0
    market_data = pd.read_csv('historical_data.csv.zip')
    generator = StrategyGenerator(config)
    print("Generating 2 unique strategies...")
    strategies = asyncio.run(generator.generate_strategies(market_data))
    print(f"Generated {sum(len(strats) for strats in strategies.values())} strategies")
    print("Strategy generation completed!")
