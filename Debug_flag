# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
import 'torch.return_types' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61840dd00>
import 'torch.fx._pytree' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61840d9a0>
import 'torch.fx.graph' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6183b78c0>
import 'torch.fx.graph_module' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6183b6360>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/__pycache__/_symbolic_trace.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/_symbolic_trace.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/__pycache__/_symbolic_trace.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/__pycache__/_lazy_graph_module.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/_lazy_graph_module.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/__pycache__/_lazy_graph_module.cpython-312.pyc'
import 'torch.fx._lazy_graph_module' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61843d070>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/__pycache__/proxy.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/proxy.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/__pycache__/proxy.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_traceback.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_traceback.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_traceback.cpython-312.pyc'
import 'torch.utils._traceback' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61843e870>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/__pycache__/traceback.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/traceback.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/__pycache__/traceback.cpython-312.pyc'
import 'torch.fx.traceback' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61843ef30>
import 'torch.fx.proxy' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61843d880>
import 'torch.fx._symbolic_trace' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6183b6a50>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/__pycache__/interpreter.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/interpreter.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/__pycache__/interpreter.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/__pycache__/config.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/config.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/__pycache__/config.cpython-312.pyc'
import 'torch.fx.config' # <_frozen_importlib_external.SourceFileLoader object at 0x72a618461910>
import 'torch.fx.interpreter' # <_frozen_importlib_external.SourceFileLoader object at 0x72a618460c80>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/__pycache__/subgraph_rewriter.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/subgraph_rewriter.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/__pycache__/subgraph_rewriter.cpython-312.pyc'
import 'torch.fx.subgraph_rewriter' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61843cdd0>
import 'torch.fx' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6183b60c0>
import 'torch.ao.quantization.utils' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6183b50d0>
import 'torch.ao.quantization.observer' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61857fa40>
import 'torch.ao.quantization.fake_quantize' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61857f230>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/__pycache__/fuse_modules.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/fuse_modules.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/__pycache__/fuse_modules.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/__pycache__/fuser_method_mappings.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/fuser_method_mappings.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/__pycache__/fuser_method_mappings.cpython-312.pyc'
import 'torch.ao.quantization.fuser_method_mappings' # <_frozen_importlib_external.SourceFileLoader object at 0x72a618462c30>
import 'torch.ao.quantization.fuse_modules' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61857f590>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/__pycache__/qconfig.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/qconfig.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/__pycache__/qconfig.cpython-312.pyc'
import 'torch.ao.quantization.qconfig' # <_frozen_importlib_external.SourceFileLoader object at 0x72a618463320>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/__pycache__/qconfig_mapping.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/qconfig_mapping.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/__pycache__/qconfig_mapping.cpython-312.pyc'
import 'torch.ao.quantization.qconfig_mapping' # <_frozen_importlib_external.SourceFileLoader object at 0x72a618462ab0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/__pycache__/quantization_mappings.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/quantization_mappings.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/__pycache__/quantization_mappings.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/__pycache__/__init__.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/modules/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/modules/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/modules/__pycache__/__init__.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/modules/__pycache__/linear.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/modules/linear.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/modules/__pycache__/linear.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/modules/__pycache__/utils.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/modules/utils.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/modules/__pycache__/utils.cpython-312.pyc'
import 'torch.ao.nn.quantized.reference.modules.utils' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6184960c0>
import 'torch.ao.nn.quantized.reference.modules.linear' # <_frozen_importlib_external.SourceFileLoader object at 0x72a618495dc0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/modules/__pycache__/conv.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/modules/conv.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/modules/__pycache__/conv.cpython-312.pyc'
import 'torch.ao.nn.quantized.reference.modules.conv' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6184965a0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/modules/__pycache__/rnn.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/modules/rnn.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/modules/__pycache__/rnn.cpython-312.pyc'
import 'torch.ao.nn.quantized.reference.modules.rnn' # <_frozen_importlib_external.SourceFileLoader object at 0x72a618496f00>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/modules/__pycache__/sparse.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/modules/sparse.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/quantized/reference/modules/__pycache__/sparse.cpython-312.pyc'
import 'torch.ao.nn.quantized.reference.modules.sparse' # <_frozen_importlib_external.SourceFileLoader object at 0x72a618497f80>
import 'torch.ao.nn.quantized.reference.modules' # <_frozen_importlib_external.SourceFileLoader object at 0x72a618495bb0>
import 'torch.ao.nn.quantized.reference' # <_frozen_importlib_external.SourceFileLoader object at 0x72a618495940>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/sparse/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/sparse/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/sparse/__pycache__/__init__.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/sparse/quantized/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/sparse/quantized/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/sparse/quantized/__pycache__/__init__.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/sparse/quantized/dynamic/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/sparse/quantized/dynamic/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/sparse/quantized/dynamic/__pycache__/__init__.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/sparse/quantized/dynamic/__pycache__/linear.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/sparse/quantized/dynamic/linear.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/sparse/quantized/dynamic/__pycache__/linear.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/sparse/quantized/__pycache__/linear.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/sparse/quantized/linear.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/sparse/quantized/__pycache__/linear.cpython-312.pyc'
import 'torch.ao.nn.sparse.quantized.linear' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182b8d70>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/sparse/quantized/__pycache__/utils.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/sparse/quantized/utils.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/ao/nn/sparse/quantized/__pycache__/utils.cpython-312.pyc'
import 'torch.ao.nn.sparse.quantized.utils' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182b9610>
import 'torch.ao.nn.sparse.quantized.dynamic.linear' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182b8740>
import 'torch.ao.nn.sparse.quantized.dynamic' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182b85c0>
import 'torch.ao.nn.sparse.quantized' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182b8350>
import 'torch.ao.nn.sparse' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6184959a0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/__pycache__/stubs.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/stubs.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/__pycache__/stubs.cpython-312.pyc'
import 'torch.ao.quantization.stubs' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182b8260>
import 'torch.ao.quantization.quantization_mappings' # <_frozen_importlib_external.SourceFileLoader object at 0x72a618495160>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/__pycache__/quantize.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/quantize.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/__pycache__/quantize.cpython-312.pyc'
import 'torch.ao.quantization.quantize' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61840ede0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/__pycache__/quantize_jit.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/quantize_jit.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/__pycache__/quantize_jit.cpython-312.pyc'
import 'torch.ao.quantization.quantize_jit' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182ba150>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/pt2e/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/pt2e/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/pt2e/__pycache__/__init__.cpython-312.pyc'
import 'torch.ao.quantization.pt2e' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182ba900>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/pt2e/__pycache__/export_utils.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/pt2e/export_utils.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/pt2e/__pycache__/export_utils.cpython-312.pyc'
import 'torch.ao.quantization.pt2e.export_utils' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182ba930>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/pt2e/__pycache__/generate_numeric_debug_handle.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/pt2e/generate_numeric_debug_handle.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/ao/quantization/pt2e/__pycache__/generate_numeric_debug_handle.cpython-312.pyc'
import 'torch.ao.quantization.pt2e.generate_numeric_debug_handle' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182bafc0>
import 'torch.ao.quantization' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61857e780>
import 'torch.quantization.quantize' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6184c2120>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/quantization/__pycache__/observer.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/quantization/observer.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/quantization/__pycache__/observer.cpython-312.pyc'
import 'torch.quantization.observer' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6187fb1a0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/quantization/__pycache__/qconfig.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/quantization/qconfig.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/quantization/__pycache__/qconfig.cpython-312.pyc'
import 'torch.quantization.qconfig' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182bb0b0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/quantization/__pycache__/fake_quantize.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/quantization/fake_quantize.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/quantization/__pycache__/fake_quantize.cpython-312.pyc'
import 'torch.quantization.fake_quantize' # <_frozen_importlib_external.SourceFileLoader object at 0x72a618553800>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/quantization/__pycache__/fuse_modules.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/quantization/fuse_modules.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/quantization/__pycache__/fuse_modules.cpython-312.pyc'
import 'torch.quantization.fuse_modules' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182bb110>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/quantization/__pycache__/stubs.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/quantization/stubs.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/quantization/__pycache__/stubs.cpython-312.pyc'
import 'torch.quantization.stubs' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182bb1d0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/quantization/__pycache__/quant_type.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/quantization/quant_type.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/quantization/__pycache__/quant_type.cpython-312.pyc'
import 'torch.quantization.quant_type' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182bb200>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/quantization/__pycache__/quantize_jit.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/quantization/quantize_jit.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/quantization/__pycache__/quantize_jit.cpython-312.pyc'
import 'torch.quantization.quantize_jit' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182bb290>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/quantization/__pycache__/quantization_mappings.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/quantization/quantization_mappings.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/quantization/__pycache__/quantization_mappings.cpython-312.pyc'
import 'torch.quantization.quantization_mappings' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182bb2f0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/quantization/__pycache__/fuser_method_mappings.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/quantization/fuser_method_mappings.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/quantization/__pycache__/fuser_method_mappings.cpython-312.pyc'
import 'torch.quantization.fuser_method_mappings' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182bb350>
import 'torch.quantization' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6186ea6f0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/__pycache__/quasirandom.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/quasirandom.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/__pycache__/quasirandom.cpython-312.pyc'
import 'torch.quasirandom' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182bb380>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/multiprocessing/__pycache__/_atfork.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/multiprocessing/_atfork.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/multiprocessing/__pycache__/_atfork.cpython-312.pyc'
import 'torch.multiprocessing._atfork' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182bb620>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/__pycache__/_lobpcg.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_lobpcg.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/__pycache__/_lobpcg.cpython-312.pyc'
import 'torch._lobpcg' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182bb740>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/masked/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/masked/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/masked/__pycache__/__init__.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/masked/__pycache__/_ops.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/masked/_ops.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/masked/__pycache__/_ops.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/masked/__pycache__/_docs.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/masked/_docs.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/masked/__pycache__/_docs.cpython-312.pyc'
import 'torch.masked._docs' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182edaf0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/masked/maskedtensor/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/masked/maskedtensor/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/masked/maskedtensor/__pycache__/__init__.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/masked/maskedtensor/__pycache__/binary.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/masked/maskedtensor/binary.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/masked/maskedtensor/__pycache__/binary.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/masked/maskedtensor/__pycache__/core.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/masked/maskedtensor/core.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/masked/maskedtensor/__pycache__/core.cpython-312.pyc'
import 'torch.masked.maskedtensor.core' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182ee210>
import 'torch.masked.maskedtensor.binary' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182eddc0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/masked/maskedtensor/__pycache__/passthrough.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/masked/maskedtensor/passthrough.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/masked/maskedtensor/__pycache__/passthrough.cpython-312.pyc'
import 'torch.masked.maskedtensor.passthrough' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182edfa0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/masked/maskedtensor/__pycache__/reductions.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/masked/maskedtensor/reductions.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/masked/maskedtensor/__pycache__/reductions.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/masked/maskedtensor/__pycache__/creation.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/masked/maskedtensor/creation.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/masked/maskedtensor/__pycache__/creation.cpython-312.pyc'
import 'torch.masked.maskedtensor.creation' # <_frozen_importlib_external.SourceFileLoader object at 0x72a618325340>
import 'torch.masked.maskedtensor.reductions' # <_frozen_importlib_external.SourceFileLoader object at 0x72a618324da0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/masked/maskedtensor/__pycache__/unary.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/masked/maskedtensor/unary.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/masked/maskedtensor/__pycache__/unary.cpython-312.pyc'
import 'torch.masked.maskedtensor.unary' # <_frozen_importlib_external.SourceFileLoader object at 0x72a618326900>
import 'torch.masked.maskedtensor' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182edbb0>
import 'torch.masked._ops' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182eca10>
import 'torch.masked' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182ec830>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/export/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/export/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/export/__pycache__/__init__.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/__pycache__/__init__.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/__pycache__/graph_drawer.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/graph_drawer.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/__pycache__/graph_drawer.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/__pycache__/shape_prop.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/shape_prop.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/__pycache__/shape_prop.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_dispatch/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_dispatch/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_dispatch/__pycache__/__init__.cpython-312.pyc'
import 'torch._dispatch' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61835c6e0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_dispatch/__pycache__/python.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_dispatch/python.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_dispatch/__pycache__/python.cpython-312.pyc'
# /usr/local/python/3.12.1/lib/python3.12/unittest/__pycache__/mock.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/unittest/mock.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/unittest/__pycache__/mock.cpython-312.pyc'
import 'unittest.mock' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61835d040>
import 'torch._dispatch.python' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61835c7a0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/__pycache__/_guards.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_guards.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/__pycache__/_guards.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/weak.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/weak.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/weak.cpython-312.pyc'
import 'torch.utils.weak' # <_frozen_importlib_external.SourceFileLoader object at 0x72a618392c30>
import 'torch._guards' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61835fce0>
import 'torch.fx.passes.shape_prop' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61835c1a0>
import 'torch.fx.passes.graph_drawer' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61834b620>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/__pycache__/graph_manipulation.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/graph_manipulation.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/__pycache__/graph_manipulation.cpython-312.pyc'
import 'torch.fx.passes.graph_manipulation' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61834b8c0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/__pycache__/net_min_base.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/net_min_base.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/__pycache__/net_min_base.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/__pycache__/split_utils.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/split_utils.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/__pycache__/split_utils.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/utils/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/utils/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/utils/__pycache__/__init__.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/utils/__pycache__/common.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/utils/common.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/utils/__pycache__/common.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/utils/__pycache__/matcher_utils.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/utils/matcher_utils.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/utils/__pycache__/matcher_utils.cpython-312.pyc'
import 'torch.fx.passes.utils.matcher_utils' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6181cb050>
import 'torch.fx.passes.utils.common' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6181cad50>
import 'torch.fx.passes.utils' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6181cab40>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/__pycache__/tools_common.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/tools_common.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/__pycache__/tools_common.cpython-312.pyc'
import 'torch.fx.passes.tools_common' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6181cb6e0>
import 'torch.fx.passes.split_utils' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6181ca720>
import 'torch.fx.passes.net_min_base' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6181c93a0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/__pycache__/operator_support.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/operator_support.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/__pycache__/operator_support.cpython-312.pyc'
import 'torch.fx.passes.operator_support' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6181f0440>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/__pycache__/param_fetch.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/param_fetch.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/__pycache__/param_fetch.cpython-312.pyc'
import 'torch.fx.passes.param_fetch' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6181f0dd0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/__pycache__/reinplace.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/reinplace.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/__pycache__/reinplace.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_subclasses/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_subclasses/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_subclasses/__pycache__/__init__.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_subclasses/__pycache__/fake_tensor.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_subclasses/fake_tensor.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_subclasses/__pycache__/fake_tensor.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_custom_op/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_custom_op/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_custom_op/__pycache__/__init__.cpython-312.pyc'
import 'torch._custom_op' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6181f3f20>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_logging/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_logging/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_logging/__pycache__/__init__.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_logging/__pycache__/_registrations.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_logging/_registrations.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_logging/__pycache__/_registrations.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_logging/__pycache__/_internal.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_logging/_internal.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_logging/__pycache__/_internal.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_logging/__pycache__/structured.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_logging/structured.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_logging/__pycache__/structured.cpython-312.pyc'
import 'torch._logging.structured' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61820db80>
import 'torch._logging._internal' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61820c350>
import 'torch._logging._registrations' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61820c140>
import 'torch._logging' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6181f3fe0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_subclasses/__pycache__/meta_utils.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_subclasses/meta_utils.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_subclasses/__pycache__/meta_utils.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_mode_utils.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_mode_utils.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_mode_utils.cpython-312.pyc'
import 'torch.utils._mode_utils' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61820f950>
import 'torch._subclasses.meta_utils' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61820e2d0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_stats.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_stats.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_stats.cpython-312.pyc'
import 'torch.utils._stats' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61820fa40>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_subclasses/__pycache__/fake_impls.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_subclasses/fake_impls.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_subclasses/__pycache__/fake_impls.cpython-312.pyc'
import 'torch._subclasses.fake_impls' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6182377d0>
import 'torch._subclasses.fake_tensor' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6181f1940>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_subclasses/__pycache__/fake_utils.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_subclasses/fake_utils.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_subclasses/__pycache__/fake_utils.cpython-312.pyc'
import 'torch._subclasses.fake_utils' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6181f3a10>
import 'torch._subclasses' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6181f17c0>
import 'torch.fx.passes.reinplace' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6181f0fb0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/__pycache__/runtime_assert.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/runtime_assert.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/__pycache__/runtime_assert.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/__pycache__/_utils.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/_utils.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/__pycache__/_utils.cpython-312.pyc'
import 'torch.fx._utils' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61827d4f0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/experimental/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/experimental/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/experimental/__pycache__/__init__.cpython-312.pyc'
import 'torch.fx.experimental' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61827d730>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/experimental/__pycache__/sym_node.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/experimental/sym_node.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/experimental/__pycache__/sym_node.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/experimental/__pycache__/_sym_dispatch_mode.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/experimental/_sym_dispatch_mode.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/experimental/__pycache__/_sym_dispatch_mode.cpython-312.pyc'
import 'torch.fx.experimental._sym_dispatch_mode' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6180b88c0>
import 'torch.fx.experimental.sym_node' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61827d790>
import 'torch.fx.passes.runtime_assert' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61827d010>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/__pycache__/split_module.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/split_module.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/__pycache__/split_module.cpython-312.pyc'
import 'torch.fx.passes.split_module' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6180e9520>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/__pycache__/splitter_base.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/splitter_base.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/__pycache__/splitter_base.cpython-312.pyc'
import 'torch.fx.passes.splitter_base' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6180e9be0>
import 'torch.fx.passes' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61834b140>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/infra/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/infra/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/infra/__pycache__/__init__.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/infra/__pycache__/pass_manager.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/infra/pass_manager.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/infra/__pycache__/pass_manager.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/infra/__pycache__/pass_base.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/infra/pass_base.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/passes/infra/__pycache__/pass_base.cpython-312.pyc'
import 'torch.fx.passes.infra.pass_base' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6180eb8c0>
import 'torch.fx.passes.infra.pass_manager' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6180eb110>
import 'torch.fx.passes.infra' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6180eb080>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/export/__pycache__/dynamic_shapes.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/export/dynamic_shapes.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/export/__pycache__/dynamic_shapes.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/export/__pycache__/exported_program.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/export/exported_program.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/export/__pycache__/exported_program.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/export/__pycache__/_tree_utils.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/export/_tree_utils.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/export/__pycache__/_tree_utils.cpython-312.pyc'
import 'torch.export._tree_utils' # <_frozen_importlib_external.SourceFileLoader object at 0x72a618116840>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/experimental/__pycache__/proxy_tensor.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/experimental/proxy_tensor.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/experimental/__pycache__/proxy_tensor.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/experimental/__pycache__/_backward_state.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/experimental/_backward_state.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/experimental/__pycache__/_backward_state.cpython-312.pyc'
import 'torch.fx.experimental._backward_state' # <_frozen_importlib_external.SourceFileLoader object at 0x72a618139430>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_cxx_pytree.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_cxx_pytree.cpython-312.pyc'
# destroy torch.utils._cxx_pytree
import 'torch.fx.experimental.proxy_tensor' # <_frozen_importlib_external.SourceFileLoader object at 0x72a618116a20>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/export/__pycache__/graph_signature.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/export/graph_signature.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/export/__pycache__/graph_signature.cpython-312.pyc'
import 'torch.export.graph_signature' # <_frozen_importlib_external.SourceFileLoader object at 0x72a618138cb0>
import 'torch.export.exported_program' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6181153d0>
import 'torch.export.dynamic_shapes' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61834af30>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/export/__pycache__/unflatten.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/export/unflatten.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/export/__pycache__/unflatten.cpython-312.pyc'
import 'torch.export.unflatten' # <_frozen_importlib_external.SourceFileLoader object at 0x72a618164ad0>
import 'torch.export' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61834ad20>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_higher_order_ops/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_higher_order_ops/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_higher_order_ops/__pycache__/__init__.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_higher_order_ops/__pycache__/cond.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_higher_order_ops/cond.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_higher_order_ops/__pycache__/cond.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_subclasses/__pycache__/functional_tensor.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_subclasses/functional_tensor.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_subclasses/__pycache__/functional_tensor.cpython-312.pyc'
import 'torch._subclasses.functional_tensor' # <_frozen_importlib_external.SourceFileLoader object at 0x72a618166c00>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_higher_order_ops/__pycache__/utils.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_higher_order_ops/utils.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_higher_order_ops/__pycache__/utils.cpython-312.pyc'
import 'torch._higher_order_ops.utils' # <_frozen_importlib_external.SourceFileLoader object at 0x72a618199490>
import 'torch._higher_order_ops.cond' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6181666f0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_higher_order_ops/__pycache__/while_loop.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_higher_order_ops/while_loop.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_higher_order_ops/__pycache__/while_loop.cpython-312.pyc'
import 'torch._higher_order_ops.while_loop' # <_frozen_importlib_external.SourceFileLoader object at 0x72a618199a60>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_higher_order_ops/__pycache__/flex_attention.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_higher_order_ops/flex_attention.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_higher_order_ops/__pycache__/flex_attention.cpython-312.pyc'
import 'torch._higher_order_ops.flex_attention' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61819a300>
import 'torch._higher_order_ops' # <_frozen_importlib_external.SourceFileLoader object at 0x72a618166510>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/__pycache__/_meta_registrations.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_meta_registrations.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/__pycache__/_meta_registrations.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_decomp/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_decomp/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_decomp/__pycache__/__init__.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_decomp/__pycache__/decompositions.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_decomp/decompositions.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_decomp/__pycache__/decompositions.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_prims/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_prims/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_prims/__pycache__/__init__.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_prims/__pycache__/debug_prims.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_prims/debug_prims.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_prims/__pycache__/debug_prims.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_content_store.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/utils/_content_store.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/utils/__pycache__/_content_store.cpython-312.pyc'
import 'torch.utils._content_store' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61805bf80>
import 'torch._prims.debug_prims' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61805bd10>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_prims/__pycache__/rng_prims.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_prims/rng_prims.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_prims/__pycache__/rng_prims.cpython-312.pyc'
import 'torch._prims.rng_prims' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6180746e0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_prims_common/__pycache__/wrappers.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_prims_common/wrappers.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_prims_common/__pycache__/wrappers.cpython-312.pyc'
import 'torch._prims_common.wrappers' # <_frozen_importlib_external.SourceFileLoader object at 0x72a618075070>
import 'torch._prims' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61801d8b0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_higher_order_ops/__pycache__/out_dtype.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_higher_order_ops/out_dtype.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_higher_order_ops/__pycache__/out_dtype.cpython-312.pyc'
import 'torch._higher_order_ops.out_dtype' # <_frozen_importlib_external.SourceFileLoader object at 0x72a617cd20c0>
import 'torch._decomp.decompositions' # <_frozen_importlib_external.SourceFileLoader object at 0x72a617fcbb90>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_refs/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_refs/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_refs/__pycache__/__init__.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_refs/__pycache__/_conversions.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_refs/_conversions.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_refs/__pycache__/_conversions.cpython-312.pyc'
import 'torch._refs._conversions' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6178d2b10>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_refs/__pycache__/fft.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_refs/fft.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_refs/__pycache__/fft.cpython-312.pyc'
import 'torch._refs.fft' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6178d3bc0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_refs/linalg/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_refs/linalg/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_refs/linalg/__pycache__/__init__.cpython-312.pyc'
import 'torch._refs.linalg' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61790e750>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_refs/nn/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_refs/nn/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_refs/nn/__pycache__/__init__.cpython-312.pyc'
import 'torch._refs.nn' # <_frozen_importlib_external.SourceFileLoader object at 0x72a617924b00>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_refs/nn/functional/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_refs/nn/functional/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_refs/nn/functional/__pycache__/__init__.cpython-312.pyc'
import 'torch._refs.nn.functional' # <_frozen_importlib_external.SourceFileLoader object at 0x72a617924a70>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_refs/special/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_refs/special/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_refs/special/__pycache__/__init__.cpython-312.pyc'
import 'torch._refs.special' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61795d940>
import 'torch._refs' # <_frozen_importlib_external.SourceFileLoader object at 0x72a617c8c3e0>
import 'torch._decomp' # <_frozen_importlib_external.SourceFileLoader object at 0x72a617fc8b60>
import 'torch._meta_registrations' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61819b200>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/func/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/func/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/func/__pycache__/__init__.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_functorch/__pycache__/eager_transforms.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_functorch/eager_transforms.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_functorch/__pycache__/eager_transforms.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/experimental/__pycache__/const_fold.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/fx/experimental/const_fold.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/fx/experimental/__pycache__/const_fold.cpython-312.pyc'
import 'torch.fx.experimental.const_fold' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61774c5f0>
import 'torch._functorch.eager_transforms' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6176dec90>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_functorch/__pycache__/functional_call.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_functorch/functional_call.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_functorch/__pycache__/functional_call.cpython-312.pyc'
import 'torch._functorch.functional_call' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61774cd40>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/_functorch/__pycache__/batch_norm_replacement.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/_functorch/batch_norm_replacement.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/_functorch/__pycache__/batch_norm_replacement.cpython-312.pyc'
import 'torch._functorch.batch_norm_replacement' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61774d280>
import 'torch.func' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6176deba0>
# /home/<USER>/.local/lib/python3.12/site-packages/torch/compiler/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/torch/compiler/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/torch/compiler/__pycache__/__init__.cpython-312.pyc'
import 'torch.compiler' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61774d2b0>
import 'torch' # <_frozen_importlib_external.SourceFileLoader object at 0x72a633b21fa0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/pykalman/__pycache__/__init__.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/pykalman/__init__.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/pykalman/__pycache__/__init__.cpython-312.pyc'
# /usr/local/python/3.12.1/lib/python3.12/site-packages/pykalman/__pycache__/standard.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/pykalman/standard.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/pykalman/__pycache__/standard.cpython-312.pyc'
# /usr/local/python/3.12.1/lib/python3.12/site-packages/pykalman/__pycache__/utils.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/pykalman/utils.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/pykalman/__pycache__/utils.cpython-312.pyc'
import 'pykalman.utils' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61774da00>
import 'pykalman.standard' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6329041d0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/pykalman/__pycache__/unscented.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/pykalman/unscented.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/pykalman/__pycache__/unscented.cpython-312.pyc'
import 'pykalman.unscented' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61774dc10>
import 'pykalman' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6328d3ad0>
# /home/<USER>/.local/lib/python3.12/site-packages/sklearn/gaussian_process/__pycache__/__init__.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/sklearn/gaussian_process/__init__.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/sklearn/gaussian_process/__pycache__/__init__.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/sklearn/gaussian_process/__pycache__/kernels.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/sklearn/gaussian_process/kernels.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/sklearn/gaussian_process/__pycache__/kernels.cpython-312.pyc'
import 'sklearn.gaussian_process.kernels' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61774eb10>
# /home/<USER>/.local/lib/python3.12/site-packages/sklearn/gaussian_process/__pycache__/_gpc.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/sklearn/gaussian_process/_gpc.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/sklearn/gaussian_process/__pycache__/_gpc.cpython-312.pyc'
# /home/<USER>/.local/lib/python3.12/site-packages/sklearn/__pycache__/multiclass.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/sklearn/multiclass.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/sklearn/__pycache__/multiclass.cpython-312.pyc'
import 'sklearn.multiclass' # <_frozen_importlib_external.SourceFileLoader object at 0x72a617785970>
import 'sklearn.gaussian_process._gpc' # <_frozen_importlib_external.SourceFileLoader object at 0x72a617784fb0>
# /home/<USER>/.local/lib/python3.12/site-packages/sklearn/gaussian_process/__pycache__/_gpr.cpython-312.pyc matches /home/<USER>/.local/lib/python3.12/site-packages/sklearn/gaussian_process/_gpr.py
# code object from '/home/<USER>/.local/lib/python3.12/site-packages/sklearn/gaussian_process/__pycache__/_gpr.cpython-312.pyc'
import 'sklearn.gaussian_process._gpr' # <_frozen_importlib_external.SourceFileLoader object at 0x72a617787200>
import 'sklearn.gaussian_process' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61774e8a0>
import 'market_simulator' # <_frozen_importlib_external.SourceFileLoader object at 0x72a63bf173b0>
# /usr/local/python/3.12.1/lib/python3.12/encodings/__pycache__/cp437.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/encodings/cp437.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/encodings/__pycache__/cp437.cpython-312.pyc'
import 'encodings.cp437' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61774e720>
# /workspaces/Trading-Bot2/__pycache__/strategy_manager.cpython-312.pyc matches /workspaces/Trading-Bot2/strategy_manager.py
# code object from '/workspaces/Trading-Bot2/__pycache__/strategy_manager.cpython-312.pyc'
import 'strategy_manager' # <_frozen_importlib_external.SourceFileLoader object at 0x72a648eb9af0>
# /usr/local/python/3.12.1/lib/python3.12/__pycache__/tracemalloc.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/tracemalloc.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/__pycache__/tracemalloc.cpython-312.pyc'
import '_tracemalloc' # <class '_frozen_importlib.BuiltinImporter'>
import 'tracemalloc' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6175b5e50>
import 'backtester' # <_frozen_importlib_external.SourceFileLoader object at 0x72a648eb8e30>
# /workspaces/Trading-Bot2/__pycache__/market_maker.cpython-312.pyc matches /workspaces/Trading-Bot2/market_maker.py
# code object from '/workspaces/Trading-Bot2/__pycache__/market_maker.cpython-312.pyc'
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/__init__.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__init__.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/__init__.cpython-312.pyc'
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/__pycache__/__init__.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/__init__.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/__pycache__/__init__.cpython-312.pyc'
import 'ccxt.async_support.base' # <_frozen_importlib_external.SourceFileLoader object at 0x72a60ba1ebd0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/__pycache__/exchange.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/exchange.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/__pycache__/exchange.cpython-312.pyc'
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/__pycache__/throttler.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/throttler.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/__pycache__/throttler.cpython-312.pyc'
import 'ccxt.async_support.base.throttler' # <_frozen_importlib_external.SourceFileLoader object at 0x72a60ba36b40>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/ws/__pycache__/__init__.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/ws/__init__.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/ws/__pycache__/__init__.cpython-312.pyc'
import 'ccxt.async_support.base.ws' # <_frozen_importlib_external.SourceFileLoader object at 0x72a60ba36e10>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/ws/__pycache__/functions.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/ws/functions.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/ws/__pycache__/functions.cpython-312.pyc'
import 'ccxt.async_support.base.ws.functions' # <_frozen_importlib_external.SourceFileLoader object at 0x72a60ba37320>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/ws/__pycache__/fast_client.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/ws/fast_client.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/ws/__pycache__/fast_client.cpython-312.pyc'
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/ws/__pycache__/aiohttp_client.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/ws/aiohttp_client.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/ws/__pycache__/aiohttp_client.cpython-312.pyc'
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/ws/__pycache__/client.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/ws/client.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/ws/__pycache__/client.cpython-312.pyc'
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/ws/__pycache__/future.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/ws/future.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/ws/__pycache__/future.cpython-312.pyc'
import 'ccxt.async_support.base.ws.future' # <_frozen_importlib_external.SourceFileLoader object at 0x72a60ba68110>
import 'ccxt.async_support.base.ws.client' # <_frozen_importlib_external.SourceFileLoader object at 0x72a60ba37b30>
import 'ccxt.async_support.base.ws.aiohttp_client' # <_frozen_importlib_external.SourceFileLoader object at 0x72a60ba37890>
import 'ccxt.async_support.base.ws.fast_client' # <_frozen_importlib_external.SourceFileLoader object at 0x72a60ba375c0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/ws/__pycache__/order_book.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/ws/order_book.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/ws/__pycache__/order_book.cpython-312.pyc'
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/ws/__pycache__/order_book_side.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/ws/order_book_side.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/base/ws/__pycache__/order_book_side.cpython-312.pyc'
import 'ccxt.async_support.base.ws.order_book_side' # <_frozen_importlib_external.SourceFileLoader object at 0x72a60ba684a0>
import 'ccxt.async_support.base.ws.order_book' # <_frozen_importlib_external.SourceFileLoader object at 0x72a60ba37ef0>
import 'ccxt.async_support.base.exchange' # <_frozen_importlib_external.SourceFileLoader object at 0x72a60ba1ec00>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/ace.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/ace.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/ace.cpython-312.pyc'
import 'ccxt.async_support.ace' # <_frozen_importlib_external.SourceFileLoader object at 0x72a60ba69dc0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/alpaca.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/alpaca.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/alpaca.cpython-312.pyc'
import 'ccxt.async_support.alpaca' # <_frozen_importlib_external.SourceFileLoader object at 0x72a60ba6a660>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/ascendex.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/ascendex.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/ascendex.cpython-312.pyc'
import 'ccxt.async_support.ascendex' # <_frozen_importlib_external.SourceFileLoader object at 0x72a60ba6af00>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bequant.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/bequant.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bequant.cpython-312.pyc'
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/hitbtc.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/hitbtc.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/hitbtc.cpython-312.pyc'
import 'ccxt.async_support.hitbtc' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616ec01a0>
import 'ccxt.async_support.bequant' # <_frozen_importlib_external.SourceFileLoader object at 0x72a60ba6bfb0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bigone.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/bigone.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bigone.cpython-312.pyc'
import 'ccxt.async_support.bigone' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616ec1580>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/binance.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/binance.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/binance.cpython-312.pyc'
import 'ccxt.async_support.binance' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616ec1e50>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/binancecoinm.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/binancecoinm.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/binancecoinm.cpython-312.pyc'
import 'ccxt.async_support.binancecoinm' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616f156d0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/binanceus.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/binanceus.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/binanceus.cpython-312.pyc'
import 'ccxt.async_support.binanceus' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616f15820>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/binanceusdm.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/binanceusdm.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/binanceusdm.cpython-312.pyc'
import 'ccxt.async_support.binanceusdm' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616f15940>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bingx.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/bingx.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bingx.cpython-312.pyc'
import 'ccxt.async_support.bingx' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616f15bb0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bit2c.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/bit2c.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bit2c.cpython-312.pyc'
import 'ccxt.async_support.bit2c' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616f17230>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitbank.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/bitbank.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitbank.cpython-312.pyc'
import 'ccxt.async_support.bitbank' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616f17b30>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitbns.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/bitbns.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitbns.cpython-312.pyc'
import 'ccxt.async_support.bitbns' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616f582f0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitcoincom.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/bitcoincom.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitcoincom.cpython-312.pyc'
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/fmfwio.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/fmfwio.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/fmfwio.cpython-312.pyc'
import 'ccxt.async_support.fmfwio' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616f58bf0>
import 'ccxt.async_support.bitcoincom' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616f58a40>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitfinex.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/bitfinex.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitfinex.cpython-312.pyc'
import 'ccxt.async_support.bitfinex' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616f58c50>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitfinex2.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/bitfinex2.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitfinex2.cpython-312.pyc'
import 'ccxt.async_support.bitfinex2' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616f59b50>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitflyer.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/bitflyer.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitflyer.cpython-312.pyc'
import 'ccxt.async_support.bitflyer' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616f5ae40>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitget.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/bitget.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitget.cpython-312.pyc'
import 'ccxt.async_support.bitget' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616f5b5c0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bithumb.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/bithumb.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bithumb.cpython-312.pyc'
import 'ccxt.async_support.bithumb' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616dc5250>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitmart.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/bitmart.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitmart.cpython-312.pyc'
import 'ccxt.async_support.bitmart' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616dc5850>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitmex.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/bitmex.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitmex.cpython-312.pyc'
import 'ccxt.async_support.bitmex' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616dc7050>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitopro.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/bitopro.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitopro.cpython-312.pyc'
import 'ccxt.async_support.bitopro' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616dc7ce0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitpanda.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/bitpanda.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitpanda.cpython-312.pyc'
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/onetrading.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/onetrading.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/onetrading.cpython-312.pyc'
import 'ccxt.async_support.onetrading' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616e1c9e0>
import 'ccxt.async_support.bitpanda' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616e1c890>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitrue.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/bitrue.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitrue.cpython-312.pyc'
import 'ccxt.async_support.bitrue' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616e1d8e0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitso.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/bitso.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitso.cpython-312.pyc'
import 'ccxt.async_support.bitso' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616e1ef30>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitstamp.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/bitstamp.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitstamp.cpython-312.pyc'
import 'ccxt.async_support.bitstamp' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616e1f680>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitteam.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/bitteam.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitteam.cpython-312.pyc'
import 'ccxt.async_support.bitteam' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616e68740>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitvavo.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/bitvavo.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bitvavo.cpython-312.pyc'
import 'ccxt.async_support.bitvavo' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616e68ec0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bl3p.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/bl3p.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bl3p.cpython-312.pyc'
import 'ccxt.async_support.bl3p' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616e69e50>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/blockchaincom.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/blockchaincom.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/blockchaincom.cpython-312.pyc'
import 'ccxt.async_support.blockchaincom' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616e6a1e0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/blofin.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/blofin.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/blofin.cpython-312.pyc'
import 'ccxt.async_support.blofin' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616e6ad20>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/btcalpha.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/btcalpha.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/btcalpha.cpython-312.pyc'
import 'ccxt.async_support.btcalpha' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616e6b9e0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/btcbox.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/btcbox.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/btcbox.cpython-312.pyc'
import 'ccxt.async_support.btcbox' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616cc0380>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/btcmarkets.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/btcmarkets.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/btcmarkets.cpython-312.pyc'
import 'ccxt.async_support.btcmarkets' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616cc0b90>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/btcturk.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/btcturk.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/btcturk.cpython-312.pyc'
import 'ccxt.async_support.btcturk' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616cc1640>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bybit.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/bybit.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/bybit.cpython-312.pyc'
import 'ccxt.async_support.bybit' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616cc1d60>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/cex.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/cex.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/cex.cpython-312.pyc'
import 'ccxt.async_support.cex' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616cc3f50>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/coinbase.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/coinbase.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/coinbase.cpython-312.pyc'
import 'ccxt.async_support.coinbase' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616d188f0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/coinbaseadvanced.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/coinbaseadvanced.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/coinbaseadvanced.cpython-312.pyc'
import 'ccxt.async_support.coinbaseadvanced' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616d19fa0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/coinbaseexchange.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/coinbaseexchange.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/coinbaseexchange.cpython-312.pyc'
import 'ccxt.async_support.coinbaseexchange' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616d1a090>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/coinbaseinternational.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/coinbaseinternational.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/coinbaseinternational.cpython-312.pyc'
import 'ccxt.async_support.coinbaseinternational' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616d1ac60>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/coincatch.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/coincatch.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/coincatch.cpython-312.pyc'
import 'ccxt.async_support.coincatch' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616d1bc20>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/coincheck.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/coincheck.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/coincheck.cpython-312.pyc'
import 'ccxt.async_support.coincheck' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616d61130>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/coinex.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/coinex.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/coinex.cpython-312.pyc'
import 'ccxt.async_support.coinex' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616d61700>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/coinlist.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/coinlist.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/coinlist.cpython-312.pyc'
import 'ccxt.async_support.coinlist' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616d62f60>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/coinmate.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/coinmate.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/coinmate.cpython-312.pyc'
import 'ccxt.async_support.coinmate' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616d63ec0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/coinmetro.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/coinmetro.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/coinmetro.cpython-312.pyc'
import 'ccxt.async_support.coinmetro' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616bc0860>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/coinone.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/coinone.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/coinone.cpython-312.pyc'
import 'ccxt.async_support.coinone' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616bc12e0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/coinsph.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/coinsph.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/coinsph.cpython-312.pyc'
import 'ccxt.async_support.coinsph' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616bc1880>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/coinspot.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/coinspot.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/coinspot.cpython-312.pyc'
import 'ccxt.async_support.coinspot' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616bc37d0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/cryptocom.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/cryptocom.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/cryptocom.cpython-312.pyc'
import 'ccxt.async_support.cryptocom' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616bc3ce0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/currencycom.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/currencycom.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/currencycom.cpython-312.pyc'
import 'ccxt.async_support.currencycom' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616c10f50>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/delta.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/delta.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/delta.cpython-312.pyc'
import 'ccxt.async_support.delta' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616c11e20>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/deribit.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/deribit.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/deribit.cpython-312.pyc'
import 'ccxt.async_support.deribit' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616c13140>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/digifinex.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/digifinex.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/digifinex.cpython-312.pyc'
import 'ccxt.async_support.digifinex' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616c4c4a0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/exmo.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/exmo.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/exmo.cpython-312.pyc'
import 'ccxt.async_support.exmo' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616c4d940>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/gate.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/gate.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/gate.cpython-312.pyc'
import 'ccxt.async_support.gate' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616c4e3f0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/gateio.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/gateio.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/gateio.cpython-312.pyc'
import 'ccxt.async_support.gateio' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616ab09b0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/gemini.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/gemini.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/gemini.cpython-312.pyc'
import 'ccxt.async_support.gemini' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616ab0aa0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/hashkey.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/hashkey.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/hashkey.cpython-312.pyc'
import 'ccxt.async_support.hashkey' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616ab1640>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/hollaex.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/hollaex.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/hollaex.cpython-312.pyc'
import 'ccxt.async_support.hollaex' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616ad0b90>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/htx.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/htx.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/htx.cpython-312.pyc'
import 'ccxt.async_support.htx' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616ad1340>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/huobi.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/huobi.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/huobi.cpython-312.pyc'
import 'ccxt.async_support.huobi' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616ad2e10>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/huobijp.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/huobijp.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/huobijp.cpython-312.pyc'
import 'ccxt.async_support.huobijp' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616ad2f00>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/hyperliquid.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/hyperliquid.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/hyperliquid.cpython-312.pyc'
import 'ccxt.async_support.hyperliquid' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616ad3aa0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/idex.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/idex.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/idex.cpython-312.pyc'
import 'ccxt.async_support.idex' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616b4cc50>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/independentreserve.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/independentreserve.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/independentreserve.cpython-312.pyc'
import 'ccxt.async_support.independentreserve' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616b4d910>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/indodax.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/indodax.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/indodax.cpython-312.pyc'
import 'ccxt.async_support.indodax' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616b4de80>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/kraken.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/kraken.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/kraken.cpython-312.pyc'
import 'ccxt.async_support.kraken' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616b4e690>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/krakenfutures.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/krakenfutures.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/krakenfutures.cpython-312.pyc'
import 'ccxt.async_support.krakenfutures' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616b4fd70>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/kucoin.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/kucoin.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/kucoin.cpython-312.pyc'
import 'ccxt.async_support.kucoin' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616b989b0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/kucoinfutures.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/kucoinfutures.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/kucoinfutures.cpython-312.pyc'
import 'ccxt.async_support.kucoinfutures' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616b99f40>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/kuna.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/kuna.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/kuna.cpython-312.pyc'
import 'ccxt.async_support.kuna' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616b9b110>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/latoken.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/latoken.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/latoken.cpython-312.pyc'
import 'ccxt.async_support.latoken' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616b9bc20>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/lbank.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/lbank.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/lbank.cpython-312.pyc'
import 'ccxt.async_support.lbank' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6169f4710>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/luno.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/luno.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/luno.cpython-312.pyc'
import 'ccxt.async_support.luno' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6169f5400>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/lykke.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/lykke.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/lykke.cpython-312.pyc'
import 'ccxt.async_support.lykke' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6169f5ac0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/mercado.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/mercado.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/mercado.cpython-312.pyc'
import 'ccxt.async_support.mercado' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6169f6240>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/mexc.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/mexc.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/mexc.cpython-312.pyc'
import 'ccxt.async_support.mexc' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6169f68d0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/ndax.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/ndax.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/ndax.cpython-312.pyc'
import 'ccxt.async_support.ndax' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616a58230>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/novadax.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/novadax.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/novadax.cpython-312.pyc'
import 'ccxt.async_support.novadax' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616a58b60>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/oceanex.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/oceanex.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/oceanex.cpython-312.pyc'
import 'ccxt.async_support.oceanex' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616a596a0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/okcoin.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/okcoin.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/okcoin.cpython-312.pyc'
import 'ccxt.async_support.okcoin' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616a59f10>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/okx.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/okx.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/okx.cpython-312.pyc'
import 'ccxt.async_support.okx' # <_frozen_importlib_external.SourceFileLoader object at 0x72a616a5ad80>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/oxfun.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/oxfun.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/oxfun.cpython-312.pyc'
import 'ccxt.async_support.oxfun' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6156a0fe0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/p2b.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/p2b.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/p2b.cpython-312.pyc'
import 'ccxt.async_support.p2b' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6156a27b0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/paradex.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/paradex.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/paradex.cpython-312.pyc'
import 'ccxt.async_support.paradex' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6156a2fc0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/paymium.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/paymium.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/paymium.cpython-312.pyc'
import 'ccxt.async_support.paymium' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6156a3e60>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/phemex.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/phemex.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/phemex.cpython-312.pyc'
import 'ccxt.async_support.phemex' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6156dc440>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/poloniex.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/poloniex.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/poloniex.cpython-312.pyc'
import 'ccxt.async_support.poloniex' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6156dd3d0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/poloniexfutures.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/poloniexfutures.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/poloniexfutures.cpython-312.pyc'
import 'ccxt.async_support.poloniexfutures' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6156de000>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/probit.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/probit.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/probit.cpython-312.pyc'
import 'ccxt.async_support.probit' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6156de930>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/timex.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/timex.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/timex.cpython-312.pyc'
import 'ccxt.async_support.timex' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6156df4d0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/tokocrypto.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/tokocrypto.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/tokocrypto.cpython-312.pyc'
import 'ccxt.async_support.tokocrypto' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6156dfcb0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/tradeogre.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/tradeogre.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/tradeogre.cpython-312.pyc'
import 'ccxt.async_support.tradeogre' # <_frozen_importlib_external.SourceFileLoader object at 0x72a615742de0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/upbit.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/upbit.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/upbit.cpython-312.pyc'
import 'ccxt.async_support.upbit' # <_frozen_importlib_external.SourceFileLoader object at 0x72a615743290>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/vertex.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/vertex.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/vertex.cpython-312.pyc'
import 'ccxt.async_support.vertex' # <_frozen_importlib_external.SourceFileLoader object at 0x72a615743cb0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/wavesexchange.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/wavesexchange.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/wavesexchange.cpython-312.pyc'
import 'ccxt.async_support.wavesexchange' # <_frozen_importlib_external.SourceFileLoader object at 0x72a615778b90>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/wazirx.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/wazirx.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/wazirx.cpython-312.pyc'
import 'ccxt.async_support.wazirx' # <_frozen_importlib_external.SourceFileLoader object at 0x72a615779790>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/whitebit.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/whitebit.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/whitebit.cpython-312.pyc'
import 'ccxt.async_support.whitebit' # <_frozen_importlib_external.SourceFileLoader object at 0x72a615779f40>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/woo.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/woo.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/woo.cpython-312.pyc'
import 'ccxt.async_support.woo' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61577aea0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/woofipro.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/woofipro.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/woofipro.cpython-312.pyc'
import 'ccxt.async_support.woofipro' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6155c4710>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/xt.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/xt.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/xt.cpython-312.pyc'
import 'ccxt.async_support.xt' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6155c5b20>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/yobit.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/yobit.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/yobit.cpython-312.pyc'
import 'ccxt.async_support.yobit' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6155c73b0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/zaif.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/zaif.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/zaif.cpython-312.pyc'
import 'ccxt.async_support.zaif' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6155c7ad0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/zonda.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/zonda.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/ccxt/async_support/__pycache__/zonda.cpython-312.pyc'
import 'ccxt.async_support.zonda' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6155c7fb0>
import 'ccxt.async_support' # <_frozen_importlib_external.SourceFileLoader object at 0x72a60ba1c7a0>
# /workspaces/Trading-Bot2/__pycache__/wallet.cpython-312.pyc matches /workspaces/Trading-Bot2/wallet.py
# code object from '/workspaces/Trading-Bot2/__pycache__/wallet.cpython-312.pyc'
# /workspaces/Trading-Bot2/__pycache__/rate_limiter.cpython-312.pyc matches /workspaces/Trading-Bot2/rate_limiter.py
# code object from '/workspaces/Trading-Bot2/__pycache__/rate_limiter.cpython-312.pyc'
import 'rate_limiter' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6156092e0>
import 'wallet' # <_frozen_importlib_external.SourceFileLoader object at 0x72a615608ef0>
# /workspaces/Trading-Bot2/__pycache__/order_book.cpython-312.pyc matches /workspaces/Trading-Bot2/order_book.py
# code object from '/workspaces/Trading-Bot2/__pycache__/order_book.cpython-312.pyc'
# /usr/local/python/3.12.1/lib/python3.12/site-packages/sortedcontainers/__pycache__/__init__.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/sortedcontainers/__init__.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/sortedcontainers/__pycache__/__init__.cpython-312.pyc'
# /usr/local/python/3.12.1/lib/python3.12/site-packages/sortedcontainers/__pycache__/sortedlist.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/sortedcontainers/sortedlist.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/sortedcontainers/__pycache__/sortedlist.cpython-312.pyc'
import 'sortedcontainers.sortedlist' # <_frozen_importlib_external.SourceFileLoader object at 0x72a615609610>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/sortedcontainers/__pycache__/sortedset.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/sortedcontainers/sortedset.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/sortedcontainers/__pycache__/sortedset.cpython-312.pyc'
import 'sortedcontainers.sortedset' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61560acc0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/sortedcontainers/__pycache__/sorteddict.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/sortedcontainers/sorteddict.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/sortedcontainers/__pycache__/sorteddict.cpython-312.pyc'
import 'sortedcontainers.sorteddict' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61560b6e0>
import 'sortedcontainers' # <_frozen_importlib_external.SourceFileLoader object at 0x72a615609580>
import 'order_book' # <_frozen_importlib_external.SourceFileLoader object at 0x72a615609340>
# /workspaces/Trading-Bot2/__pycache__/inventory_manager.cpython-312.pyc matches /workspaces/Trading-Bot2/inventory_manager.py
# code object from '/workspaces/Trading-Bot2/__pycache__/inventory_manager.cpython-312.pyc'
import 'inventory_manager' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61560baa0>
# /workspaces/Trading-Bot2/__pycache__/compliance.cpython-312.pyc matches /workspaces/Trading-Bot2/compliance.py
# code object from '/workspaces/Trading-Bot2/__pycache__/compliance.cpython-312.pyc'
import 'compliance' # <_frozen_importlib_external.SourceFileLoader object at 0x72a615650230>
# /workspaces/Trading-Bot2/__pycache__/strategy_factory.cpython-312.pyc matches /workspaces/Trading-Bot2/strategy_factory.py
# code object from '/workspaces/Trading-Bot2/__pycache__/strategy_factory.cpython-312.pyc'
import 'strategy_factory' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6156503e0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/aiofiles/__pycache__/__init__.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/aiofiles/__init__.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/aiofiles/__pycache__/__init__.cpython-312.pyc'
# /usr/local/python/3.12.1/lib/python3.12/site-packages/aiofiles/threadpool/__pycache__/__init__.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/aiofiles/threadpool/__init__.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/aiofiles/threadpool/__pycache__/__init__.cpython-312.pyc'
# /usr/local/python/3.12.1/lib/python3.12/site-packages/aiofiles/__pycache__/base.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/aiofiles/base.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/aiofiles/__pycache__/base.cpython-312.pyc'
import 'aiofiles.base' # <_frozen_importlib_external.SourceFileLoader object at 0x72a615650b60>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/aiofiles/threadpool/__pycache__/binary.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/aiofiles/threadpool/binary.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/aiofiles/threadpool/__pycache__/binary.cpython-312.pyc'
# /usr/local/python/3.12.1/lib/python3.12/site-packages/aiofiles/threadpool/__pycache__/utils.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/aiofiles/threadpool/utils.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/aiofiles/threadpool/__pycache__/utils.cpython-312.pyc'
import 'aiofiles.threadpool.utils' # <_frozen_importlib_external.SourceFileLoader object at 0x72a615651220>
import 'aiofiles.threadpool.binary' # <_frozen_importlib_external.SourceFileLoader object at 0x72a615650fe0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/aiofiles/threadpool/__pycache__/text.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/aiofiles/threadpool/text.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/aiofiles/threadpool/__pycache__/text.cpython-312.pyc'
import 'aiofiles.threadpool.text' # <_frozen_importlib_external.SourceFileLoader object at 0x72a615652ba0>
import 'aiofiles.threadpool' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6156508c0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/aiofiles/tempfile/__pycache__/__init__.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/aiofiles/tempfile/__init__.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/aiofiles/tempfile/__pycache__/__init__.cpython-312.pyc'
# /usr/local/python/3.12.1/lib/python3.12/site-packages/aiofiles/tempfile/__pycache__/temptypes.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/aiofiles/tempfile/temptypes.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/aiofiles/tempfile/__pycache__/temptypes.cpython-312.pyc'
import 'aiofiles.tempfile.temptypes' # <_frozen_importlib_external.SourceFileLoader object at 0x72a615670830>
import 'aiofiles.tempfile' # <_frozen_importlib_external.SourceFileLoader object at 0x72a615670140>
import 'aiofiles' # <_frozen_importlib_external.SourceFileLoader object at 0x72a615650740>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/__pycache__/__init__.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/__init__.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/__pycache__/__init__.cpython-312.pyc'
import 'watchdog' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6156712e0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/observers/__pycache__/__init__.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/observers/__init__.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/observers/__pycache__/__init__.cpython-312.pyc'
# /usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/utils/__pycache__/__init__.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/utils/__init__.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/utils/__pycache__/__init__.cpython-312.pyc'
import 'watchdog.utils' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6156715b0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/utils/__pycache__/platform.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/utils/platform.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/utils/__pycache__/platform.cpython-312.pyc'
import 'watchdog.utils.platform' # <_frozen_importlib_external.SourceFileLoader object at 0x72a615671ac0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/observers/__pycache__/inotify.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/observers/inotify.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/observers/__pycache__/inotify.cpython-312.pyc'
# /usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/__pycache__/events.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/events.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/__pycache__/events.cpython-312.pyc'
# /usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/utils/__pycache__/patterns.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/utils/patterns.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/utils/__pycache__/patterns.cpython-312.pyc'
import 'watchdog.utils.patterns' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6156731d0>
import 'watchdog.events' # <_frozen_importlib_external.SourceFileLoader object at 0x72a615672240>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/observers/__pycache__/api.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/observers/api.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/observers/__pycache__/api.cpython-312.pyc'
# /usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/utils/__pycache__/bricks.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/utils/bricks.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/utils/__pycache__/bricks.cpython-312.pyc'
import 'watchdog.utils.bricks' # <_frozen_importlib_external.SourceFileLoader object at 0x72a615698a40>
import 'watchdog.observers.api' # <_frozen_importlib_external.SourceFileLoader object at 0x72a615672390>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/observers/__pycache__/inotify_buffer.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/observers/inotify_buffer.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/observers/__pycache__/inotify_buffer.cpython-312.pyc'
# /usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/observers/__pycache__/inotify_c.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/observers/inotify_c.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/observers/__pycache__/inotify_c.cpython-312.pyc'
import 'watchdog.observers.inotify_c' # <_frozen_importlib_external.SourceFileLoader object at 0x72a615698fe0>
# /usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/utils/__pycache__/delayed_queue.cpython-312.pyc matches /usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/utils/delayed_queue.py
# code object from '/usr/local/python/3.12.1/lib/python3.12/site-packages/watchdog/utils/__pycache__/delayed_queue.cpython-312.pyc'
import 'watchdog.utils.delayed_queue' # <_frozen_importlib_external.SourceFileLoader object at 0x72a61569a4e0>
import 'watchdog.observers.inotify_buffer' # <_frozen_importlib_external.SourceFileLoader object at 0x72a615698ce0>
import 'watchdog.observers.inotify' # <_frozen_importlib_external.SourceFileLoader object at 0x72a615671ca0>
import 'watchdog.observers' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6156713a0>
import 'market_maker' # <_frozen_importlib_external.SourceFileLoader object at 0x72a6175b76b0>
import 'trading_system' # <_frozen_importlib_external.SourceFileLoader object at 0x72a64ca51430>
Terminated