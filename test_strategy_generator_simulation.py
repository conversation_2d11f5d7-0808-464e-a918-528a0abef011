#!/usr/bin/env python3
"""
Test script that simulates the strategy generator functionality
without requiring external AI APIs
"""
import json
import os
import shutil
import hashlib
from typing import Dict, List, Any

def backup_strategies_file():
    """Create a backup of the strategies file"""
    if os.path.exists('strategies.json'):
        shutil.copy('strategies.json', 'strategies_backup.json')
        print("✅ Created backup of strategies.json")
        return True
    return False

def restore_strategies_file():
    """Restore the strategies file from backup"""
    if os.path.exists('strategies_backup.json'):
        shutil.copy('strategies_backup.json', 'strategies.json')
        os.remove('strategies_backup.json')
        print("✅ Restored strategies.json from backup")
        return True
    return False

def generate_mock_strategies(strategy_type: str) -> List[Dict[str, Any]]:
    """Generate mock strategies for testing (simulates AI generation)"""
    
    # Define base templates for different strategy types
    templates = {
        "trend_following": {
            "market_parameters": {
                "signal_strengths": {
                    "trend_strength_score": 0.7,
                    "breakout_strength_multi": 1.2,
                    "momentum_persistence_bias": 0.6
                },
                "directional_biases": {
                    "breakout_direction_confidence": 0.8,
                    "volatility_regime_bias": 0.3
                },
                "action_sensitivities": {
                    "trailing_stop_sensitivity": 0.02,
                    "risk_aversion": 0.4,
                    "position_sizing_aggressiveness": 1.3,
                    "drawdown_recovery_sensitivity": 0.7,
                    "time_of_day_sensitivity": 0.5
                }
            },
            "calculation_parameters": {
                "MOVING_AVERAGE_SHORT": 20,
                "MOVING_AVERAGE_LONG": 50,
                "TREND_CONFIRMATION_PERIOD": 15,
                "trend_strength_window": 10,
                "breakout_period": 25,
                "volume_ma_period": 15,
                "atr_period": 20
            }
        },
        "momentum": {
            "market_parameters": {
                "signal_strengths": {
                    "momentum_score": 0.8,
                    "volume_momentum_weight": 0.6,
                    "momentum_persistence_bias": 0.7
                },
                "directional_biases": {
                    "RSI_directional_bias": 0.4,
                    "MACD_directional_bias": 0.5,
                    "volatility_regime_bias": 0.2
                },
                "action_sensitivities": {
                    "risk_aversion": 0.3,
                    "position_sizing_aggressiveness": 1.5,
                    "drawdown_recovery_sensitivity": 0.6,
                    "noise_filter_sensitivity": 0.3
                }
            },
            "calculation_parameters": {
                "MOMENTUM_PERIOD": 14,
                "RSI_PERIOD": 14,
                "RSI_OVERBOUGHT": 70,
                "RSI_OVERSOLD": 30,
                "MACD_FAST": 12,
                "MACD_SLOW": 26,
                "MACD_SIGNAL": 9,
                "volume_ma_period": 20,
                "noise_filter_window": 12
            }
        }
    }
    
    if strategy_type not in templates:
        strategy_type = "trend_following"  # Default fallback
    
    template = templates[strategy_type]
    timeframes = ["short_term", "mid_term", "long_term", "seasonal_term"]
    
    strategies = []
    
    for i, timeframe in enumerate(timeframes):
        # Create variations for each timeframe
        strategy = {
            "name": f"{strategy_type.replace('_', ' ').title()} {timeframe.replace('_', ' ').title()} Test",
            "description": f"A test {strategy_type} strategy optimized for {timeframe} trading",
            "patterns": [strategy_type],
            "parameters": {
                "market_parameters": template["market_parameters"].copy(),
                "calculation_parameters": template["calculation_parameters"].copy()
            },
            "timeframe": timeframe,
            "market_conditions": {
                "optimal_volatility": f"0.0{i+1}-0.{i+2}0",
                "optimal_trend": "bullish" if i % 2 == 0 else "bearish",
                "optimal_liquidity": f"≥{1.0 + i * 0.2:.1f}"
            }
        }
        
        # Adjust parameters based on timeframe
        if timeframe == "short_term":
            strategy["parameters"]["calculation_parameters"]["MOVING_AVERAGE_SHORT"] = 10
            strategy["parameters"]["calculation_parameters"]["MOVING_AVERAGE_LONG"] = 20
        elif timeframe == "long_term":
            strategy["parameters"]["calculation_parameters"]["MOVING_AVERAGE_SHORT"] = 50
            strategy["parameters"]["calculation_parameters"]["MOVING_AVERAGE_LONG"] = 200
        elif timeframe == "seasonal_term":
            strategy["parameters"]["calculation_parameters"]["MOVING_AVERAGE_SHORT"] = 100
            strategy["parameters"]["calculation_parameters"]["MOVING_AVERAGE_LONG"] = 500
        
        strategies.append(strategy)
    
    return strategies

def simulate_strategy_generation_and_save(strategy_type: str) -> bool:
    """Simulate the full strategy generation and saving process"""
    print(f"\n🤖 Simulating strategy generation for: {strategy_type}")
    
    try:
        # Step 1: Generate mock strategies
        generated_strategies = generate_mock_strategies(strategy_type)
        print(f"✅ Generated {len(generated_strategies)} strategies")
        
        # Step 2: Load existing strategies
        with open('strategies.json', 'r') as f:
            existing_strategies = json.load(f)
        
        original_count = len(existing_strategies)
        print(f"📊 Original strategy count: {original_count}")
        
        # Step 3: Add new strategies with unique keys
        added_keys = []
        for strategy in generated_strategies:
            # Generate unique key
            strategy_data = json.dumps(strategy, sort_keys=True)
            hash_key = hashlib.md5(strategy_data.encode()).hexdigest()[:6]
            strategy_key = f"test_{strategy_type}_{strategy['timeframe']}_{hash_key}"
            
            # Add to existing strategies
            existing_strategies[strategy_key] = strategy
            added_keys.append(strategy_key)
            
            print(f"   ➕ Added: {strategy['name']} ({strategy_key})")
        
        # Step 4: Save updated strategies
        with open('strategies.json', 'w') as f:
            json.dump(existing_strategies, f, indent=4)
        
        new_count = len(existing_strategies)
        print(f"📊 New strategy count: {new_count}")
        print(f"✅ Successfully added {new_count - original_count} strategies")
        
        # Step 5: Verify strategies were saved correctly
        with open('strategies.json', 'r') as f:
            verification_strategies = json.load(f)
        
        all_found = True
        for key in added_keys:
            if key not in verification_strategies:
                print(f"❌ Strategy {key} not found after saving")
                all_found = False
        
        if all_found:
            print("✅ All strategies verified in saved file")
        
        return all_found, added_keys
        
    except Exception as e:
        print(f"❌ Strategy generation simulation failed: {e}")
        return False, []

def test_strategy_generation_workflow():
    """Test the complete strategy generation workflow"""
    print("🚀 Testing Strategy Generation Workflow")
    
    strategy_types = ["trend_following", "momentum"]
    all_added_keys = []
    
    for strategy_type in strategy_types:
        success, added_keys = simulate_strategy_generation_and_save(strategy_type)
        if success:
            all_added_keys.extend(added_keys)
        else:
            return False, []
    
    print(f"\n📊 Total strategies added: {len(all_added_keys)}")
    return True, all_added_keys

def cleanup_test_strategies(strategy_keys: List[str]):
    """Remove test strategies from the file"""
    print(f"\n🧹 Cleaning up {len(strategy_keys)} test strategies...")
    
    try:
        with open('strategies.json', 'r') as f:
            strategies = json.load(f)
        
        removed_count = 0
        for key in strategy_keys:
            if key in strategies:
                del strategies[key]
                removed_count += 1
        
        with open('strategies.json', 'w') as f:
            json.dump(strategies, f, indent=4)
        
        print(f"✅ Removed {removed_count} test strategies")
        return True
        
    except Exception as e:
        print(f"❌ Failed to cleanup test strategies: {e}")
        return False

def main():
    """Run the complete test"""
    print("🚀 Starting Strategy Generator Simulation Test\n")
    
    # Create backup
    backup_created = backup_strategies_file()
    
    try:
        # Test the workflow
        success, added_keys = test_strategy_generation_workflow()
        
        if success:
            print("\n🎉 Strategy generation workflow test PASSED!")
            
            # Cleanup test strategies
            cleanup_test_strategies(added_keys)
            
            return True
        else:
            print("\n❌ Strategy generation workflow test FAILED!")
            return False
            
    finally:
        # Restore backup
        if backup_created:
            restore_strategies_file()

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
