import time
import pandas as pd
from config import Config
import zipfile
import io
from trading_system import TradingSystem
import asyncio
import decimal as Decimal
import logging
import google.generativeai as genai
import os
import sys
from strategy_generator import StrategyGenerator
import psutil
from api_call_manager import APICallManager

def log_memory_usage():
    process = psutil.Process()
    memory_info = process.memory_info()
    print(f"Memory usage: {memory_info.rss / 1024 / 1024:.2f} MB")

# Setup logging only once
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def load_historical_data(filename='historical_data.csv.zip'):
    print("Starting to load historical data")
    try:
        with zipfile.ZipFile(filename) as z:
            print(f"Zip file opened, contents: {z.namelist()}")
            csv_filename = z.namelist()[0]

            chunks = []
            with z.open(csv_filename) as f:
                for chunk in pd.read_csv(f, chunksize=10000):
                    chunk['timestamp'] = pd.to_datetime(chunk['timestamp'])
                    chunk.set_index('timestamp', inplace=True)
                    chunk = chunk.dropna()
                    chunks.append(chunk)
                    print(f"Processed chunk {len(chunks)}, rows in chunk: {len(chunk)}")
                    if len(chunks) >= 10:
                        combined = pd.concat(chunks)
                        chunks = [combined]
                final_df = pd.concat(chunks) if len(chunks) > 1 else chunks[0]
                print(f"Final data shape: {final_df.shape}")
                return final_df

    except MemoryError:
        print("Memory error: The dataset is too large for available RAM")
        raise
    except zipfile.BadZipFile:
        print("The zip file is corrupted or invalid")
        raise
    except Exception as e:
        print(f"Error loading historical data: {str(e)}")
        raise

def validate_and_prepare_data(data: pd.DataFrame) -> pd.DataFrame:
    logger.debug(f"Initial data shape: {data.shape}")
    logger.debug(f"Columns present: {data.columns.tolist()}")

    required_columns = ['open', 'high', 'low', 'close', 'volume']
    data = data.reindex(columns=required_columns)
    data = data.dropna()

    for col in required_columns:
        data[col] = pd.to_numeric(data[col], errors='coerce')

    logger.debug(f"Processed data shape: {data.shape}")
    return data

# ✅ Dummy API call for testing
async def dummy_api_call():
    await asyncio.sleep(1)
    print("✅ Dummy API call executed.")
    return "dummy-result"

async def test_gemini_rate_limit():
    genai.configure(api_key=os.environ["GOOGLE_AI_API_KEY"])
    model = genai.GenerativeModel("models/gemini-1.5-pro")

    success_count = 0
    attempt = 0

    while success_count < 5:
        attempt += 1
        try:
            logger.info(f"Attempt #{attempt}: Calling Gemini API...")
            response = await asyncio.to_thread(model.generate_content, "Say something interesting.")
            print(f"✅ Success #{success_count + 1}: {response.text.strip()}")
            success_count += 1
        except Exception as e:
            logger.warning(f"❌ API call failed on attempt #{attempt}: {e}")
        await asyncio.sleep(10)

    logger.info("🎉 Completed 5 successful calls. Test finished.")

# ✅ Main entrypoint
async def main():
    try:
        api_key = os.environ.get('GOOGLE_AI_API_KEY')
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('models/gemini-1.5-pro')
        config = Config()
        api_call_manager = APICallManager()

        print("Starting API manager...")
        await api_call_manager.start()

        print("Loading historical data...")
        historical_data = load_historical_data()

        print("Processing data...")
        processed_data = validate_and_prepare_data(historical_data)

        print("Initializing trading system...")
        trading_system = TradingSystem(config, processed_data, api_call_manager)

        print("Starting trading system...")
        await trading_system.start()

    except Exception as e:
        logger.error(f"Fatal error: {str(e)}", exc_info=True)

# ✅ Launch the asyncio loop
if __name__ == "__main__":
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(main())
