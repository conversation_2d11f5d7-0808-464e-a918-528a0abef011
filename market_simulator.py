import numpy as np
import pandas as pd
from scipy.stats import skewnorm, t, norm, levy_stable, beta, multivariate_normal
from statsmodels.tsa.arima.model import ARIMA
from arch import arch_model
from sklearn.preprocessing import MinMaxScaler
import networkx as nx
from numba import jit
from copulas.multivariate import GaussianMultivariate
from statsmodels.tsa.regime_switching.markov_regression import MarkovRegression
from scipy.stats import gamma
from scipy.optimize import minimize
from sklearn.cluster import KMeans
from collections import deque
import torch
import torch.nn as nn
from pykalman import KalmanFilter
from prophet import Prophet
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import RBF, WhiteKernel, Matern
from scipy.integrate import odeint
import asyncio
import logging
from strategy import Strategy, TimeFrame
from config import Config
from typing import Dict, List

class MarketSimulator:
    def __init__(self, config, strategies: Dict[TimeFrame, List[Strategy]]):
        self.config = config
        self.strategies = strategies
        self.endog_data = np.array([])  # Initialized with an empty array to avoid NoneType issues
        self.rng = np.random.default_rng()
        self.copula = GaussianMultivariate()
        self.price_scaler = MinMaxScaler(feature_range=(1, 1000))
        self.market_graph = self._create_market_graph()
        self.regime_model = None
        self.volatility_model = self._create_volatility_model()
        self.order_book = self._create_order_book()
        self.market_maker = self._create_market_maker()
        self.liquidity_pool = self._create_liquidity_pool()
        self.neural_network = self._create_neural_network()
        self.kalman_filter = self._create_kalman_filter()
        self.prophet_model = self._create_prophet_model()
        self.gp_regressor = self._create_gp_regressor()

        # ULTRA-FAST BITCOIN SIMULATION: Optimized for 30-minute strategy optimization
        self.ultra_fast_mode = getattr(config, 'ultra_fast_mode', True)
        self.btc_simulation_cache = {}  # Cache for repeated simulations
        self.btc_base_params = self._initialize_btc_parameters()

        # Pre-generate Bitcoin market scenarios for speed
        if self.ultra_fast_mode:
            self._pregenerate_btc_scenarios()

    def _initialize_btc_parameters(self):
        """Initialize Bitcoin-specific market parameters for realistic simulation"""
        return {
            'base_price': 45000,  # Starting BTC price
            'daily_volatility': 0.04,  # 4% daily volatility (typical for BTC)
            'trend_persistence': 0.7,  # Strong trend persistence
            'mean_reversion_speed': 0.15,  # Moderate mean reversion
            'jump_probability': 0.02,  # 2% chance of price jumps per day
            'jump_magnitude': 0.08,  # 8% average jump size
            'volume_correlation': 0.6,  # Price-volume correlation
            'weekend_effect': 0.8,  # Reduced weekend activity
            'institutional_flow': 0.3,  # Institutional trading impact
            'retail_sentiment': 0.4,  # Retail sentiment impact
            'halving_cycle_effect': 0.2,  # Long-term halving cycle
            'regulatory_sensitivity': 0.25,  # Regulatory news impact
            'correlation_with_stocks': 0.3,  # Correlation with traditional markets
            'mining_difficulty_impact': 0.1,  # Mining economics impact
            'defi_yield_impact': 0.15  # DeFi yield farming impact
        }

    def _pregenerate_btc_scenarios(self, num_scenarios=10, days_per_scenario=30):
        """Pre-generate Bitcoin market scenarios for ultra-fast optimization"""
        print("🚀 Pre-generating Bitcoin market scenarios for ultra-fast optimization...")

        self.btc_scenarios = []
        for i in range(num_scenarios):
            scenario = self._generate_single_btc_scenario(days_per_scenario, scenario_id=i)
            self.btc_scenarios.append(scenario)

        print(f"✅ Generated {num_scenarios} Bitcoin scenarios ({days_per_scenario} days each)")
        
    def generate_market_data(self, days=365):
        # Initial price generation with regime switching
        prices, regimes = _generate_complex_regime_switching_prices(days)
        returns = np.diff(np.log(prices))
        self.endog_data = returns
        self._update_regime_model()

        try:
            self.sentiment_model = self._create_sentiment_model()
        except Exception as e:
            # If ARIMA fitting absolutely fails, you can fallback to None or a dummy
            self.sentiment_model = None
            # Optionally log a warning:
            self.logger.warning(f"Could not build ARIMA: {e}")

        sentiment = self._generate_sentiment(days)

        # Generate base market factors
        factors = self._generate_correlated_factors(days)
        on_chain_metrics = self._generate_on_chain_metrics(days)

        # Apply strategy impacts with stress multipliers
        strategy_weights = {
            'trend_following': 0.15,
            'mean_reversion': 0.15,
            'momentum': 0.15,
            'breakout': 0.15,
            'volatility_clustering': 0.15,
            'statistical_arbitrage': 0.15,
            'sentiment_analysis': 0.10
        }
    
        stress_multiplier = np.random.choice([1.5, 2.0, 2.5], size=days)
    
        # Apply weighted strategy impacts
        for strategy in self.strategies.values():
            for pattern in strategy.favored_patterns:
                if pattern in strategy_weights:
                    weight = strategy_weights[pattern]
                    strategy_prices = getattr(self, f'_simulate_{pattern}')(prices, strategy.parameters)
                    prices = prices + (strategy_prices - prices) * weight * stress_multiplier

        # Apply existing market effects
        prices = np.asarray(prices)
        regimes = np.asarray(regimes)
        assert isinstance(prices, np.ndarray), f"Expected ndarray for prices, got {type(prices)}"
        volumes = _generate_volume_series(days, prices, regimes)
        volatility = self._generate_volatility_series(days, prices, regimes)
        liquidity = self._generate_liquidity(volumes, prices)
        order_book = self._simulate_order_book(days, prices, volumes, volatility, liquidity)
    
        # Apply sophisticated market dynamics
        prices, volumes = self._apply_network_effects(prices, volumes)
        prices = _apply_long_term_cycles(prices)
        prices = self._apply_jump_diffusion(prices)
        prices = self._simulate_flash_crash(prices)
        prices = self.price_scaler.fit_transform(prices.reshape(-1, 1)).flatten()
        prices = self._add_microstructure_noise(prices)
    
        # Generate additional market features
        funding_rates = self._simulate_funding_rate(days, prices, volumes)
        exchange_prices = self._simulate_multiple_exchanges(prices)
        prices, volumes = self._apply_time_of_day_effects(prices, volumes)
        prices = self._simulate_news_events(prices, sentiment)
        prices = self._simulate_regulatory_events(prices)
    
        # Apply advanced market mechanics
        order_book = self._simulate_spoofing(order_book)
        liquidity_pool_data = self._simulate_liquidity_pool(days, prices)
    
        # Apply ML predictions and forecasts
        prices = self._apply_neural_network_prediction(prices)
        prices = np.asarray(prices)[-days:]
        prices = self._apply_kalman_filter(prices)
        prices = np.asarray(prices)[-days:]
        high = prices + prices * volatility * np.random.uniform(0.5, 1.5, size=days)
        low  = prices - prices * volatility * np.random.uniform(0.5, 1.5, size=days)
        prophet_forecast = np.asarray(self._generate_prophet_forecast(days, prices))
        # trim or pad to exactly `days`
        if prophet_forecast.size < days:
            pad = np.full(days - prophet_forecast.size, np.nan)
            prophet_forecast = np.concatenate([pad, prophet_forecast])
        else:
            prophet_forecast = prophet_forecast[-days:]

        gp_forecast = np.asarray(self._generate_gp_forecast(days, prices, horizon=30))
        if gp_forecast.size < days:
            pad = np.full(days - gp_forecast.size, np.nan)
            gp_forecast = np.concatenate([pad, gp_forecast])
        else:
            gp_forecast = gp_forecast[-days:]
        lotka_volterra = self._simulate_lotka_volterra(days)
        fractional_brownian = self._generate_fractional_brownian_motion(days)

        expected = days
        lengths = {
            'prices':              len(prices),
            'volumes':             len(volumes),
            'volatility':          len(volatility),
            'regimes':             len(regimes),
            'sentiment':           len(sentiment),
            # break out all four on-chain metrics
            'active_addresses':    on_chain_metrics.shape[0],
            'transaction_volume':  on_chain_metrics.shape[0],
            'unique_senders':      on_chain_metrics.shape[0],
            'gas_used':            on_chain_metrics.shape[0],
            'funding_rates':       len(funding_rates),
            'lotka_volterra':      len(lotka_volterra),
            'fractional_brownian': len(fractional_brownian),
            'prophet_forecast':    len(prophet_forecast),
            'gp_forecast':         len(gp_forecast),
        }
        # also check each list‐of‐length‐days
        for k, v in lengths.items():
            if v != expected:
                raise ValueError(f"Length mismatch for {k}: got {v}, expected {expected}")

        # Return the complete market dataset
        print("Dataset completed")
        return pd.DataFrame({
            'price': prices,
            'high':  high,
            'low':   low,
            'volume': volumes,
            'volatility': volatility,
            'regime': regimes,
            'sentiment': sentiment,
            'active_addresses': on_chain_metrics[:, 0],
            'transaction_volume': on_chain_metrics[:, 1],
            'unique_senders': on_chain_metrics[:, 2],
            'gas_used': on_chain_metrics[:, 3],
            'funding_rate': funding_rates,
            'order_book_imbalance': self._calculate_order_book_imbalance(order_book),
            'liquidity': liquidity,
            'bid': order_book['bids'][0],
            'ask': order_book['asks'][0],
            'bid_volume': order_book['bid_volumes'][0],
            'ask_volume': order_book['ask_volumes'][0],
            'exchange_1_price': exchange_prices[0],
            'exchange_2_price': exchange_prices[1],
            'exchange_3_price': exchange_prices[2],
            'active_addresses': on_chain_metrics[:, 0],
            'transaction_volume': on_chain_metrics[:, 1],
            'liquidity_pool_reserve_0': liquidity_pool_data['reserve_0'],
            'liquidity_pool_reserve_1': liquidity_pool_data['reserve_1'],
            'liquidity_pool_k': liquidity_pool_data['k'],
            'prophet_forecast': prophet_forecast,
            'gp_forecast': gp_forecast,
            'lotka_volterra': lotka_volterra,
            'fractional_brownian': fractional_brownian
        })

    def _create_market_graph(self):
        G = nx.barabasi_albert_graph(n=100, m=2)
        for (u, v) in G.edges():
            G.edges[u,v]['weight'] = self.rng.uniform(0.1, 1.0)
        return G

    def _create_regime_model(self):
        if self.endog_data.size == 0:  # Check if endog_data is populated
            raise ValueError("endog_data is not defined. Generate market data first.")
        return MarkovRegression(endog=self.endog_data, k_regimes=4, trend='c', switching_variance=True, switching_exog=True)

    def _update_regime_model(self):
        if self.regime_model is None:
            self.regime_model = self._create_regime_model()
        self.regime_model = self.regime_model.fit()

    def _create_volatility_model(self):
        return arch_model(y=None, vol='Garch', p=1, o=1, q=1, dist='skewt')

    def _create_sentiment_model(self):
        if self.endog_data.size == 0:
            raise ValueError("endog_data is not defined. Generate market data first.")
        return ARIMA(order=(1, 1, 1), endog=self.endog_data)

    def _create_order_book(self):
        return {'bids': deque(), 'asks': deque()}

    def _create_market_maker(self):
        return {'inventory': 0, 'cash': 1000000}

    def _create_liquidity_pool(self):
        return {'reserve_0': 1000000, 'reserve_1': 1000000, 'k': 1000000 * 1000000}

    def _generate_correlated_factors(self, days):
        data = np.random.normal(0, 1, size=(days, 5))
        self.copula.fit(data)
        factors = self.copula.sample(days).values
        factors[:, 0] = skewnorm.ppf(norm.cdf(factors[:, 0]), a=-2, loc=50, scale=10)
        factors[:, 1] = gamma.ppf(norm.cdf(factors[:, 1]), a=2, scale=0.5)
        factors[:, 2] = t.ppf(norm.cdf(factors[:, 2]), df=3, loc=0, scale=0.001)
        factors[:, 3] = levy_stable.ppf(norm.cdf(factors[:, 3]), alpha=1.5, beta=0)
        factors[:, 4] = beta.ppf(norm.cdf(factors[:, 4]), a=2, b=5)
        return factors

    def _generate_on_chain_metrics(self, days):
        active_addresses = np.cumsum(np.random.normal(1000, 100, days))
        transaction_volume = np.exp(np.random.normal(10, 1, days))
        unique_senders = np.cumsum(np.random.poisson(500, days))
        gas_used = np.cumsum(np.random.gamma(2, 1000000, days))
        return np.column_stack((active_addresses, transaction_volume, unique_senders, gas_used))

    def _generate_sentiment(self, days: int) -> np.ndarray:
        """
        If sentiment_model is available, fit/simulate; otherwise return a flat zeros array.
        """
        if self.sentiment_model is None:
            return np.zeros(days)  # or small random noise

        # Now sentiment_model is a fitted ARIMA object (from statsmodels or similar)
        sim_array = np.zeros(days, dtype=np.float64)
        try:
            # e.g. statsmodels' ARIMAResults has .simulate() or .sample()
            for i in range(days):
                sim_array[i] = self.sentiment_model.simulate(1)[0]
            return np.tanh(sim_array)  # normalize to [-1,1]
        except Exception:
            # fallback if fit/simulation breaks
            return np.zeros(days)

    def _simulate_trend_following(self, prices, params):
        """
        Trend‐following simulation with robust handling of short series
        and avoiding NaNs or divide‐by‐zero errors.
        """
        df = pd.DataFrame({'price': prices})
        # Ensure window sizes are positive integers
        short_w = max(1, int(params.get('MOVING_AVERAGE_SHORT', 10)))
        long_w  = max(1, int(params.get('MOVING_AVERAGE_LONG', 30)))

        # Compute rolling means with at least one period
        short_ma = df['price'].rolling(window=short_w, min_periods=1).mean()
        long_ma  = df['price'].rolling(window=long_w,  min_periods=1).mean()

        # Replace zero to prevent div/0, then compute trend strength
        long_ma = long_ma.replace(0, np.nan)
        ts     = (short_ma - long_ma) / long_ma
        ts     = ts.fillna(0)  # fallback zero when undefined

        thresh = float(params.get('TREND_STRENGTH_THRESHOLD', 0.1))
        return prices * (1 + ts * thresh)

    def _simulate_mean_reversion(self, prices, params):
        df = pd.DataFrame({'price': prices})

        # Get and validate MEAN_WINDOW
        try:
         window = int(float(params.get('MEAN_WINDOW', 20)))
        except (ValueError, TypeError):
            print("⚠️ Invalid MEAN_WINDOW value. Using default of 20.")
            window = 20
        if window <= 0:
            raise ValueError(f"Invalid MEAN_WINDOW: {window}. Must be a positive integer.")

        mean_price = df['price'].rolling(window=window).mean()
        deviation = (df['price'] - mean_price) / mean_price

        threshold = params.get('MEAN_REVERSION_THRESHOLD', 0.05)
        return prices * (1 - deviation * threshold)


    def _simulate_momentum(self, prices, params):
        df = pd.DataFrame({'price': prices})
        momentum = df['price'].pct_change(periods=params.get('MOMENTUM_PERIOD', 14))
        return prices * (1 + momentum * params.get('MOMENTUM_THRESHOLD', 0.05))

    def _simulate_breakout(self, prices, params):
        df = pd.DataFrame({'price': prices})
        upper_band = df['price'].rolling(window=params.get('BREAKOUT_PERIOD', 20)).max()
        lower_band = df['price'].rolling(window=params.get('BREAKOUT_PERIOD', 20)).min()
        breakout = np.where(prices > upper_band, 1.01, np.where(prices < lower_band, 0.99, 1))
        return prices * breakout

    def _simulate_volatility_clustering(self, prices, params):
        df = pd.DataFrame({'price': prices})
        volatility = df['price'].pct_change().rolling(params.get('VOLATILITY_WINDOW', 20)).std()
        vol_ratio = volatility / volatility.rolling(100).mean()
        return prices * np.where(vol_ratio > params.get('HIGH_VOLATILITY_THRESHOLD', 1.5), 1.1, 0.9)

    def _simulate_statistical_arbitrage(self, prices, params):
        asset2 = prices * (1 + np.random.normal(0, 0.01, len(prices)))
        df = pd.DataFrame({'asset1': prices, 'asset2': asset2})
        spread = df['asset1'] - df['asset2']
        z_score = (spread - spread.rolling(params.get('LOOKBACK_PERIOD', 20)).mean()) / \
        spread.rolling(params.get('LOOKBACK_PERIOD', 20)).std()
        return prices * (1 + z_score / params.get('Z_SCORE_THRESHOLD', 2))

    def _simulate_sentiment_analysis(self, prices, params):
        sentiment_impact = self._generate_sentiment(len(prices)) * params.get('SENTIMENT_IMPACT_WEIGHT', 0.3)
        return prices * (1 + sentiment_impact)

    def _apply_strategy_patterns(self, prices, regimes):
        original_prices = prices.copy()
    
        for strategy in self.strategies.values():
            if 'trend_following' in strategy.favored_patterns:
                prices = self._simulate_trend_following(prices, strategy.parameters)
            elif 'mean_reversion' in strategy.favored_patterns:
                prices = self._simulate_mean_reversion(prices, strategy.parameters)
            elif 'momentum' in strategy.favored_patterns:
                prices = self._simulate_momentum(prices, strategy.parameters)
            elif 'breakout' in strategy.favored_patterns:
                prices = self._simulate_breakout(prices, strategy.parameters)
            elif 'volatility_clustering' in strategy.favored_patterns:
                prices = self._simulate_volatility_clustering(prices, strategy.parameters)
            elif 'statistical_arbitrage' in strategy.favored_patterns:
                prices = self._simulate_statistical_arbitrage(prices, strategy.parameters)
            elif 'sentiment_analysis' in strategy.favored_patterns:
                prices = self._simulate_sentiment_analysis(prices, strategy.parameters)
    
        return prices

    def _generate_volatility_series(self, days: int, prices: np.ndarray, regimes: np.ndarray):
        # 1) raw log-returns
        # Defensive repair step
        prices = np.nan_to_num(prices, nan=1.0, posinf=1.0, neginf=1.0)
        prices = np.clip(prices, 0.01, 1e6)  # prevent log(0)

        returns = np.diff(np.log(prices))
        print("PRICE HEAD:", prices[:10])
        print("PRICE NaNs:", np.isnan(prices).sum())
        print("PRICE Zeros:", np.sum(prices == 0))
        print("RETURNS:", returns[:10])
        print("RETURNS NaNs:", np.isnan(returns).sum())
        print("RETURNS Inf:", np.isinf(returns).sum())

        # 2) keep only finite observations
        returns = returns[np.isfinite(returns)]
        if returns.size == 0:                       # extreme corner-case guard
            raise ValueError("All returns are NaN/Inf – cannot fit GARCH.")

        # 3) fit a fresh GARCH model on the cleaned series
        garch = arch_model(returns, vol="Garch", p=1, o=1, q=1, dist="skewt")
        fitted = garch.fit(disp="off")

        # 4) forecast volatility
        forecast = fitted.forecast(horizon=days)
        return np.sqrt(forecast.variance.values[-1])

    def _generate_liquidity(self, volumes, prices):
        base_liquidity = volumes * prices
        noise = np.random.normal(1, 0.1, len(volumes))
        return base_liquidity * noise

    def _simulate_order_book(self, days, prices, volumes, volatility, liquidity):
        spreads = np.maximum(0.001, np.random.normal(0.002, 0.001, size=days)) * prices
        bids = prices - spreads / 2
        asks = prices + spreads / 2
        
        depth_levels = 10
        bid_depths = [bids * (1 - i * 0.001) for i in range(depth_levels)]
        ask_depths = [asks * (1 + i * 0.001) for i in range(depth_levels)]
        
        bid_volumes = volumes[:, np.newaxis] * np.random.beta(2, 2, size=(days, depth_levels))
        ask_volumes = volumes[:, np.newaxis] * np.random.beta(2, 2, size=(days, depth_levels))
        
        volume_adjust = np.exp(volatility * 10) * (liquidity / np.mean(liquidity))
        volume_adjust = np.asarray(volume_adjust)  # 👈 convert to proper ndarray
        bid_volumes *= volume_adjust[:, np.newaxis]
        ask_volumes *= volume_adjust[:, np.newaxis]
        
        return {
            'bids': bid_depths,
            'asks': ask_depths,
            'bid_volumes': bid_volumes.T,
            'ask_volumes': ask_volumes.T
        }

    def _apply_network_effects(self, prices, volumes):
        for node in self.market_graph.nodes():
            if self.rng.random() < 0.01:
                impact = self.rng.normal(0, 0.02)
                neighbors = list(self.market_graph.neighbors(node))
                for neighbor in neighbors:
                    start = neighbor * (len(prices) // 100)
                    end = (neighbor + 1) * (len(prices) // 100)
                    edge_weight = self.market_graph.edges[node, neighbor]['weight']
                    prices[start:end] *= (1 + impact * edge_weight)
                    volumes[start:end] *= (1 + abs(impact) * edge_weight)
        return prices, volumes

    def _apply_jump_diffusion(self, prices):
        jump_times = self.rng.poisson(lam=5, size=len(prices))
        jump_sizes = self.rng.normal(0, 0.05, size=len(prices))
        prices *= np.exp(jump_times * jump_sizes)
        return prices

    def _add_microstructure_noise(self, prices):
        noise = self.rng.normal(0, 0.0001, size=len(prices))
        return prices * (1 + noise)

    def _simulate_flash_crash(self, prices, probability=0.001, crash_severity=0.1):
        crash_mask = np.random.random(len(prices)) < probability
        crash_factors = np.where(crash_mask, 1 - crash_severity, 1)
        return prices * crash_factors

    def _simulate_multiple_exchanges(self, base_prices, num_exchanges=3):
        exchange_prices = []
        for _ in range(num_exchanges):
            noise = np.random.normal(1, 0.001, len(base_prices))
            exchange_prices.append(base_prices * noise)
        return np.array(exchange_prices)

    def _simulate_spoofing(self, order_book, spoof_probability=0.01, spoof_size_factor=10):
        if np.random.random() < spoof_probability:
            spoof_side = np.random.choice(['bid', 'ask'])
            spoof_level = np.random.randint(0, len(order_book[f'{spoof_side}s']))
            order_book[f'{spoof_side}_volumes'][spoof_level] *= spoof_size_factor
        return order_book

    def _simulate_funding_rate(self, days, prices, volumes):
        base_rate = 0.0001
        price_volatility = np.std(np.diff(np.log(prices)))
        volume_factor = np.log(volumes) / np.mean(np.log(volumes))
        funding_rates = np.cumsum(np.random.normal(base_rate, price_volatility * 0.1, days))
        return funding_rates * volume_factor

    def _apply_time_of_day_effects(self, prices, volumes):
        hours = np.arange(len(prices)) % 24
        asian_session = (hours >= 1) & (hours < 9)
        european_session = (hours >= 8) & (hours < 16)
        us_session = (hours >= 14) & (hours < 22)
        
        session_factors = np.ones(len(prices))
        session_factors[asian_session] *= 1.1
        session_factors[european_session] *= 1.2
        session_factors[us_session] *= 1.3
        
        return prices * session_factors, volumes * session_factors

    def _simulate_news_events(self, prices, sentiment, probability=0.01, impact_range=(-0.05, 0.05)):
        news_mask = np.random.random(len(prices)) < probability
        news_impacts = np.random.uniform(*impact_range, size=len(prices))
        sentiment_factor = (sentiment + 1) / 2  # Normalize sentiment to [0, 1]
        return prices * (1 + news_mask * news_impacts * sentiment_factor)

    def _simulate_regulatory_events(self, prices, probability=0.001, impact_range=(-0.1, 0.1)):
        regulatory_mask = np.random.random(len(prices)) < probability
        regulatory_impacts = np.random.uniform(*impact_range, size=len(prices))
        return prices * (1 + regulatory_mask * regulatory_impacts)

    def _create_neural_network(self):
        model = nn.Sequential(
            nn.Linear(10, 50),
            nn.ReLU(),
            nn.Linear(50, 20),
            nn.ReLU(),
            nn.Linear(20, 1)
        )
        return model

    def _create_kalman_filter(self):
        return KalmanFilter(transition_matrices=[1],
                            observation_matrices=[1],
                            initial_state_mean=0,
                            initial_state_covariance=1,
                            observation_covariance=1,
                            transition_covariance=.01)

    def _create_prophet_model(self):
        return Prophet()

    def _create_gp_regressor(self):
        kernel = RBF() + WhiteKernel() + Matern(nu=1.5)
        return GaussianProcessRegressor(kernel=kernel, n_restarts_optimizer=10)

    def _apply_neural_network_prediction(self, prices):
        input_data = torch.tensor(prices[-10:]).float().unsqueeze(0)
        with torch.no_grad():
            prediction = self.neural_network(input_data).item()
        return np.append(prices, prediction)

    def _apply_kalman_filter(self, prices):
        filtered_state_means, _ = self.kalman_filter.filter(prices)
        return filtered_state_means.flatten()

    def _generate_prophet_forecast(self, days, prices: np.ndarray) -> np.ndarray:
        # We want a 30‐step forecast
        H = 30
        n = len(prices)
        if days is None:
            days = n

        # 1) Quick guard: Need at least 2 data points
        if len(prices) < 2:
            # Fallback: repeat last price (or zero if truly empty)
            last = float(prices[-1]) if prices.size > 0 else 0.0
            return np.full(H, last)

        # 2) Build DataFrame for Prophet
        df = pd.DataFrame({
            'ds': pd.date_range(start='2020-01-01', periods=len(prices), freq='D'),
            'y': prices
        }).dropna()

        # After dropna, still need at least 2 rows
        if df.shape[0] < 2:
            last = float(prices[-1])
            return np.full(H, last)

        # 3) Fit & forecast, catching any Prophet errors
        try:
            if n < 2:
                yhat = np.full(H, prices[-1] if n>0 else 0.0)
            else:
                m = Prophet()
                m.fit(df)
                future = m.make_future_dataframe(periods=H, freq='D')
                forecast = m.predict(future)
                # Return only the last H yhat values
                yhat = m.predict(future)['yhat'].values[-H:]
            # Pad front with NaN so total length = days
            if H < days:
                pad = np.full(days - H, np.nan)
                return np.concatenate([pad, yhat])
            else:
                # if days <= H, just take the last `days` values
                return yhat[-days:]
        
        except Exception as e:
            # Fallback on any error: repeat last known price
            last = float(prices[-1])
            self.logger.warning(f"[Prophet] Forecast failed ({e}); using flat forecast")
            return np.full(H, last)

    def _generate_gp_forecast(self, days, prices: np.ndarray, horizon: int = 30) -> np.ndarray:
        """
        Forecast next `horizon` points using a GaussianProcessRegressor.
        Drops NaN from the end of the series and falls back to flat forecast if needed.
        """
        if days is None:
            days = len(prices)
        
        # 1) Clean NaNs
        # Create an index array for non-NaN entries
        mask = ~np.isnan(prices)
        clean_prices = prices[mask]

        # 2) Ensure we have enough points to fit
        if clean_prices.size < 2:
            last = float(clean_prices[-1]) if clean_prices.size > 0 else 0.0
            return np.full(horizon, last)

        # 3) Prepare X (time) and y (price)
        X = np.arange(clean_prices.size).reshape(-1, 1)

        try:
            # 4) Fit the GP model
            self.gp_regressor.fit(X, clean_prices)

            # 5) Predict for the next `horizon` time steps
            X_pred = np.arange(clean_prices.size, clean_prices.size + horizon).reshape(-1, 1)
            y_pred, y_std = self.gp_regressor.predict(X_pred, return_std=True)


            if clean_prices.size < 2:
                y_pred = np.full(horizon, clean_prices[-1] if clean_prices.size>0 else 0.0)
            else:
                y_pred, _ = self.gp_regressor.predict(X_pred, return_std=True)

            # Pad front with NaN
            if horizon < days:
                pad = np.full(days - horizon, np.nan)
                return np.concatenate([pad, y_pred])
            else:
                return y_pred[-days:]
            
        except Exception as e:
            # Fallback: flat forecast at last known (clean) price
            last = float(clean_prices[-1])
            self.logger.warning(f"[GP Forecast] fit or predict failed ({e}); using flat forecast")
            return np.full(horizon, last)

    def _simulate_lotka_volterra(self, days):
        def lotka_volterra(X, t, a, b, c, d):
            x, y = X
            dxdt = a*x - b*x*y
            dydt = -c*y + d*x*y
            return [dxdt, dydt]

        t = np.linspace(0, days, days)
        X0 = [1, 1]
        a, b, c, d = 1, 0.1, 1.5, 0.75
        solution = odeint(lotka_volterra, X0, t, args=(a, b, c, d))
        return solution[:, 0]  # Return prey population

    def _generate_fractional_brownian_motion(self, days, H=0.7):
        def fbm(n, H):
            r = np.zeros(n)
            r[0] = np.random.randn()
            for i in range(1, n):
                r[i] = sum([np.random.randn() * (j+1)**(H-0.5) - j**(H-0.5) for j in range(i)])
            return r

        return fbm(days, H)

    def _simulate_liquidity_pool(self, days, prices):
        reserve_0 = np.zeros(days)
        reserve_1 = np.zeros(days)
        k = np.zeros(days)
        
        reserve_0[0] = self.liquidity_pool['reserve_0']
        reserve_1[0] = self.liquidity_pool['reserve_1']
        k[0] = self.liquidity_pool['k']
        
        for i in range(1, days):
            swap_amount = np.random.normal(0, 0.01) * reserve_0[i-1]
            if swap_amount > 0:
                reserve_0[i] = reserve_0[i-1] + swap_amount
                reserve_1[i] = k[i-1] / reserve_0[i]
            else:
                reserve_1[i] = reserve_1[i-1] - abs(swap_amount)
                reserve_0[i] = k[i-1] / reserve_1[i]
            k[i] = reserve_0[i] * reserve_1[i]
        
        return {'reserve_0': reserve_0, 'reserve_1': reserve_1, 'k': k}

    def _calculate_order_book_imbalance(self, order_book):
        total_bid_volume = np.sum(order_book['bid_volumes'])
        total_ask_volume = np.sum(order_book['ask_volumes'])
        return (total_bid_volume - total_ask_volume) / (total_bid_volume + total_ask_volume)

    def _calculate_transaction_cost(self, shares, price, volume, volatility, liquidity):
        base_cost = 0.001 * shares * price
        slippage = 0.1 * (shares / volume) * price * volatility
        liquidity_cost = 0.01 * (shares * price) / liquidity
        market_impact = self._calculate_market_impact(shares, price, volume)
        return base_cost + slippage + liquidity_cost + market_impact

    def _calculate_market_impact(self, shares, price, volume):
        impact_factor = 0.1 * (shares / volume) ** 0.5
        return impact_factor * price

    def _apply_execution_delay(self, order_time, max_delay=5):
        return order_time + np.random.randint(0, max_delay)

    def run_simulation(self, strategies: Dict[str, Strategy]):
        # 1) Generate market data
        data = self.generate_market_data(365)

        # 2) Allocate initial capital across strategies
        total_capital = float(self.rng.uniform(1e3, 1e7))
        cash_per_strat = {name: total_capital * 0.25 for name in strategies}  # 25% each
        open_positions = {name: None for name in strategies}
        daily_nav = []      # will record total NAV across all strats
        trade_history = []

        def nav(current_price):
            nav_total = 0.0
            for name, strat in strategies.items():
                cash = cash_per_strat[name]
                pos = open_positions[name]
                if pos:
                    # unrealized PnL
                    if pos['dir']=='long':
                        pnl = pos['shares'] * (current_price - pos['entry'])
                    else:
                        pnl = pos['shares'] * (pos['entry'] - current_price)
                    nav_total += cash + pnl
                else:
                    nav_total += cash
            return nav_total

        # 3) Loop through each timestamp
        for t, row in data.iterrows():
            price = row['price']

            # A) First, for each strat, check exits (stop/TP/trail)
            for name, strat in strategies.items():
                pos = open_positions[name]
                if not pos: continue

                exit_price = None
                # stop‐loss
                if pos['dir']=='long' and price <= pos['stop']: exit_price = pos['stop']
                if pos['dir']=='short' and price >= pos['stop']: exit_price = pos['stop']
                # take‐profit
                if pos['dir']=='long' and price >= pos['tp']: exit_price = price if price<pos['tp'] else pos['tp']
                if pos['dir']=='short' and price <= pos['tp']: exit_price = price if price>pos['tp'] else pos['tp']
                # trailing
                trail = pos['trail']
                if trail is not None:
                    if pos['dir']=='long' and price <= trail: exit_price = trail
                    if pos['dir']=='short' and price >= trail: exit_price = trail

                if exit_price is not None:
                    # close and record
                    if pos['dir']=='long':
                        cash_per_strat[name] += pos['shares'] * exit_price
                    else:
                        cash_per_strat[name] += pos['shares'] * (2*pos['entry'] - exit_price)
                    trade_history.append({
                        'time': t, 'strategy': name, 'action':'exit',
                        'price': exit_price, 'shares':pos['shares'], 'direction':pos['dir'],
                        'entry_price': pos['entry']  
                    })
                    open_positions[name] = None

            # B) Then, for any strat without a position, see if it wants to open
            total_nav = nav(price)
            for name, strat in strategies.items():
                if open_positions[name]: continue

                sig = strat.generate_signal(data.loc[:t])
                if sig == 0: continue

                # logic‐gate helper will compute a boolean buy/sell decision
                decision = self._evaluate_logic_gates(strat.parameters, row)
                if not decision: 
                    continue

                # allocate 25% of total nav
                alloc = 0.25 * total_nav
                shares = alloc / price
                direction = 'long' if sig>0 else 'short'

                # risk‐manager for stops/TP
                _, stop, tp = self.backtest_risk_manager.apply_backtest_risk_management(
                    sig, total_nav, price, data.loc[:t], strat
                )
                trail_pct = strat.parameters.get('TRAILING_STOP')
                trail = price*(1 - trail_pct) if direction=='long' else price*(1 + trail_pct)

                # book entry
                cash_per_strat[name] -= shares * price
                open_positions[name] = {
                    'entry': price, 'shares': shares,
                    'stop': stop, 'tp': tp, 'trail': trail,
                    'dir': direction
                }
                trade_history.append({
                    'time': t, 'strategy': name, 'action':'entry',
                    'price': price, 'shares': shares, 'direction': direction
                })

            # C) record portfolio‐level NAV
            daily_nav.append({'time': t, 'nav': nav(price)})

        # D) force‐exit any remaining positions at last price
        last_price = data['price'].iloc[-1]
        for name, pos in open_positions.items():
            if not pos: continue
            if pos['dir']=='long':
                cash_per_strat[name] += pos['shares'] * last_price
            else:
                cash_per_strat[name] += pos['shares'] * (2*pos['entry'] - last_price)
            trade_history.append({
                'time': data.index[-1], 'strategy': name, 'action':'exit',
                'price': last_price, 'shares':pos['shares'], 'direction':pos['dir']
            })

        # E) compute returns & call your performance calculator
        navs = [d['nav'] for d in daily_nav]
        returns = np.diff(navs) / navs[:-1]
        final_cash = sum(cash_per_strat.values())
        return self._calculate_performance_metrics(total_capital, final_cash, returns, trade_history)

    def _calculate_performance_metrics(self, initial_capital, final_value, daily_returns, trade_history):
        total_return = (final_value - initial_capital) / initial_capital
        sharpe_ratio = np.sqrt(252) * np.mean(daily_returns) / np.std(daily_returns)
        max_drawdown = np.min(np.minimum.accumulate(daily_returns))
        volatility = np.std(daily_returns) * np.sqrt(252)
        sortino_ratio = np.sqrt(252) * np.mean(daily_returns) / np.std([r for r in daily_returns if r < 0])
        win_rate = sum(1 for r in daily_returns if r > 0) / len(daily_returns)

        total_trades = len(trade_history)
        # correct key-based access
        profitable_trades = sum(1 for trade in trade_history if trade['action'] == 'exit' and trade['price'] > trade['entry_price'])
        gains = sum(trade['price'] - trade['entry_price'] for trade in trade_history if trade['action'] == 'exit' and trade['price'] > trade['entry_price'])
        losses = sum(trade['entry_price'] - trade['price'] for trade in trade_history if trade['action'] == 'exit' and trade['price'] <= trade['entry_price'])

        profit_factor = gains / abs(losses) if losses != 0 else float('inf')  # or 1.0, or 0.0, depending on your philosophy

        calmar_ratio = self._calculate_calmar_ratio(daily_returns)
        omega_ratio = self._calculate_omega_ratio(daily_returns)
        tail_ratio = self._calculate_tail_ratio(daily_returns)

        return {
            'final_value': final_value,
            'total_return': total_return,
            'sharpe_ratio': sharpe_ratio,
            'sortino_ratio': sortino_ratio,
            'max_drawdown': max_drawdown,
            'volatility': volatility,
            'win_rate': win_rate,
            'total_trades': total_trades,
            'profitable_trades': profitable_trades,
            'profit_factor': profit_factor,
            'calmar_ratio': calmar_ratio,
            'omega_ratio': omega_ratio,
            'tail_ratio': tail_ratio,
            'trade_history': trade_history
        }

    def _calculate_calmar_ratio(self, returns, periods=252):
        max_drawdown = self._calculate_max_drawdown(returns)
        if max_drawdown == 0:
            return np.nan
        return (np.mean(returns) * periods) / abs(max_drawdown)

    def _calculate_omega_ratio(self, returns, threshold=0):
        return np.sum(np.maximum(returns - threshold, 0)) / np.sum(np.maximum(threshold - returns, 0))

    def _calculate_tail_ratio(self, returns):
        return abs(np.percentile(returns, 95)) / abs(np.percentile(returns, 5))

    def _calculate_max_drawdown(self, returns):
        wealth_index = (1 + returns).cumprod()
        previous_peaks = np.maximum.accumulate(wealth_index)
        drawdowns = (wealth_index - previous_peaks) / previous_peaks
        return drawdowns.min()

    def optimize_strategy(self, strategy, param_ranges):
        def objective(params):
            for i, param in enumerate(strategy.parameters):
                strategy.parameters[param] = params[i]
            results = self.run_simulation(strategy)
            return -results['sharpe_ratio']  # Minimize negative Sharpe ratio

        bounds = [param_ranges[param] for param in strategy.parameters]
        result = minimize(objective, x0=[np.mean(b) for b in bounds], bounds=bounds, method='L-BFGS-B')
        
        for i, param in enumerate(strategy.parameters):
            strategy.parameters[param] = result.x[i]
        
        return strategy, -result.fun  # Return optimized strategy and its Sharpe ratio

    def ultra_fast_simulation(self, strategy) -> Dict[str, float]:
        """
        ULTRA-FAST BITCOIN SIMULATION: Optimized for 30-minute strategy optimization

        Uses pre-generated Bitcoin scenarios and simplified calculations for maximum speed
        while maintaining realistic Bitcoin market behavior characteristics.
        """
        import random

        # Use cached scenario if available
        scenario_id = random.randint(0, len(self.btc_scenarios) - 1) if hasattr(self, 'btc_scenarios') else 0

        if hasattr(self, 'btc_scenarios') and self.btc_scenarios:
            scenario = self.btc_scenarios[scenario_id]
            prices = scenario['prices']
            volumes = scenario['volumes']
            volatility = scenario['volatility']
        else:
            # Generate minimal scenario on-the-fly
            scenario = self._generate_single_btc_scenario(30, scenario_id=0)
            prices = scenario['prices']
            volumes = scenario['volumes']
            volatility = scenario['volatility']

        # Ultra-fast strategy simulation
        try:
            returns = np.diff(np.log(prices))

            # Generate simplified signals based on strategy type
            signals = self._generate_ultra_fast_signals(strategy, prices, volumes, volatility)

            # Calculate performance metrics with simplified calculations
            strategy_returns = returns[1:] * signals[:-1]  # Lag signals by 1 period

            # Core performance metrics
            total_return = np.sum(strategy_returns)
            volatility_metric = np.std(strategy_returns) if len(strategy_returns) > 1 else 0.01
            sharpe_ratio = total_return / volatility_metric if volatility_metric > 0 else 0.0

            # Simplified drawdown calculation
            cumulative_returns = np.cumsum(strategy_returns)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = cumulative_returns - running_max
            max_drawdown = np.min(drawdowns) if len(drawdowns) > 0 else 0.0

            # Win rate calculation
            winning_trades = np.sum(strategy_returns > 0)
            total_trades = len(strategy_returns)
            win_rate = winning_trades / total_trades if total_trades > 0 else 0.0

            return {
                'total_return': float(total_return),
                'sharpe_ratio': float(sharpe_ratio),
                'max_drawdown': float(max_drawdown),
                'win_rate': float(win_rate),
                'volatility': float(volatility_metric),
                'num_trades': int(total_trades),
                'simulation_type': 'ultra_fast_btc'
            }

        except Exception as e:
            # Return poor performance for failed simulations
            return {
                'total_return': -0.1,
                'sharpe_ratio': -0.5,
                'max_drawdown': -0.2,
                'win_rate': 0.3,
                'volatility': 0.05,
                'num_trades': 0,
                'simulation_type': 'ultra_fast_btc_failed',
                'error': str(e)
            }

    def _generate_single_btc_scenario(self, days: int, scenario_id: int = 0) -> Dict[str, np.ndarray]:
        """Generate a single realistic Bitcoin market scenario"""
        np.random.seed(42 + scenario_id)  # Reproducible scenarios

        params = self.btc_base_params
        prices = np.zeros(days)
        volumes = np.zeros(days)
        volatility = np.zeros(days)

        # Initialize
        prices[0] = params['base_price'] * (1 + np.random.normal(0, 0.02))  # Small random start
        volumes[0] = 1000 + np.random.exponential(500)  # Base volume
        volatility[0] = params['daily_volatility']

        for t in range(1, days):
            # Bitcoin-specific price dynamics

            # 1. Trend component with persistence
            trend = np.random.normal(0, 0.01) * params['trend_persistence']

            # 2. Mean reversion component
            price_deviation = (prices[t-1] - params['base_price']) / params['base_price']
            mean_reversion = -price_deviation * params['mean_reversion_speed']

            # 3. Volatility clustering (GARCH-like)
            volatility[t] = (0.1 * params['daily_volatility'] +
                           0.8 * volatility[t-1] +
                           0.1 * abs(np.log(prices[t-1]/prices[max(0, t-2)]) if t > 1 else 0))

            # 4. Jump component (sudden price movements)
            jump = 0
            if np.random.random() < params['jump_probability']:
                jump = np.random.choice([-1, 1]) * params['jump_magnitude'] * np.random.exponential(1)

            # 5. Weekend effect (reduced activity)
            weekend_factor = params['weekend_effect'] if (t % 7) in [5, 6] else 1.0

            # 6. Institutional vs retail flow
            institutional_flow = np.random.normal(0, 0.005) * params['institutional_flow']
            retail_sentiment = np.random.normal(0, 0.008) * params['retail_sentiment']

            # Combine all effects
            total_return = (trend + mean_reversion + jump +
                          institutional_flow + retail_sentiment) * weekend_factor

            # Apply volatility
            noise = np.random.normal(0, volatility[t])
            total_return += noise

            # Update price
            prices[t] = prices[t-1] * np.exp(total_return)

            # Generate volume correlated with price movement and volatility
            volume_base = 1000 + np.random.exponential(500)
            volume_multiplier = 1 + abs(total_return) * params['volume_correlation']
            volumes[t] = volume_base * volume_multiplier * weekend_factor

        return {
            'prices': prices,
            'volumes': volumes,
            'volatility': volatility,
            'scenario_id': scenario_id,
            'days': days
        }

    def _generate_ultra_fast_signals(self, strategy, prices: np.ndarray,
                                   volumes: np.ndarray, volatility: np.ndarray) -> np.ndarray:
        """Generate trading signals using simplified strategy logic for speed"""

        signals = np.zeros(len(prices))
        strategy_type = strategy.favored_patterns[0] if strategy.favored_patterns else "trend_following"

        # Get strategy parameters with defaults
        params = getattr(strategy, 'parameters', {})

        if strategy_type == "trend_following":
            # Simple moving average crossover
            short_window = params.get('MOVING_AVERAGE_SHORT', 10)
            long_window = params.get('MOVING_AVERAGE_LONG', 20)

            if len(prices) > long_window:
                short_ma = np.convolve(prices, np.ones(short_window)/short_window, mode='valid')
                long_ma = np.convolve(prices, np.ones(long_window)/long_window, mode='valid')

                # Align arrays
                min_len = min(len(short_ma), len(long_ma))
                signals[-min_len:] = np.where(short_ma[-min_len:] > long_ma[-min_len:], 1, -1)

        elif strategy_type == "mean_reversion":
            # RSI-based mean reversion
            rsi_period = params.get('RSI_PERIOD', 14)
            if len(prices) > rsi_period:
                returns = np.diff(prices)
                gains = np.where(returns > 0, returns, 0)
                losses = np.where(returns < 0, -returns, 0)

                avg_gain = np.convolve(gains, np.ones(rsi_period)/rsi_period, mode='valid')
                avg_loss = np.convolve(losses, np.ones(rsi_period)/rsi_period, mode='valid')

                rs = avg_gain / (avg_loss + 1e-10)
                rsi = 100 - (100 / (1 + rs))

                # Mean reversion signals
                signals[-len(rsi):] = np.where(rsi > 70, -1, np.where(rsi < 30, 1, 0))

        elif strategy_type == "momentum":
            # Price momentum
            momentum_period = params.get('MOMENTUM_PERIOD', 10)
            if len(prices) > momentum_period:
                momentum = prices[momentum_period:] / prices[:-momentum_period] - 1
                threshold = params.get('MOMENTUM_THRESHOLD', 0.02)
                signals[-len(momentum):] = np.where(momentum > threshold, 1,
                                                  np.where(momentum < -threshold, -1, 0))

        elif strategy_type == "breakout":
            # Bollinger band breakouts
            bb_period = params.get('BOLLINGER_PERIOD', 20)
            bb_std = params.get('BOLLINGER_STD', 2.0)

            if len(prices) > bb_period:
                rolling_mean = np.convolve(prices, np.ones(bb_period)/bb_period, mode='valid')
                rolling_std = np.array([np.std(prices[i:i+bb_period]) for i in range(len(prices)-bb_period+1)])

                upper_band = rolling_mean + bb_std * rolling_std
                lower_band = rolling_mean - bb_std * rolling_std

                current_prices = prices[-len(rolling_mean):]
                signals[-len(rolling_mean):] = np.where(current_prices > upper_band, 1,
                                                      np.where(current_prices < lower_band, -1, 0))

        else:
            # Default: simple trend following
            if len(prices) > 10:
                short_ma = np.convolve(prices, np.ones(5)/5, mode='valid')
                long_ma = np.convolve(prices, np.ones(10)/10, mode='valid')
                min_len = min(len(short_ma), len(long_ma))
                signals[-min_len:] = np.where(short_ma[-min_len:] > long_ma[-min_len:], 1, -1)

        return signals

    def cluster_market_regimes(self, n_clusters=3):
        market_data = self.generate_market_data(None)  # Generate some sample data
        features = market_data[['volatility', 'volume', 'sentiment', 'on_chain_metric']].values
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        clusters = kmeans.fit_predict(features)
        return clusters
    
    def _apply_timeframe_effects(self, prices, timeframe):
        TimeFrame = timeframe
        effects = {
            TimeFrame.SHORT_TERM: {
                'volatility': 0.002,
                'mean_reversion': 0.3,
                'trend_strength': 0.1
            },
            TimeFrame.MID_TERM: {
                'volatility': 0.005,
                'mean_reversion': 0.5,
                'trend_strength': 0.3
            },
            TimeFrame.LONG_TERM: {
                'volatility': 0.01,
                'mean_reversion': 0.7,
                'trend_strength': 0.6
            },
            TimeFrame.SEASONAL_TERM: {
                'volatility': 0.015,
                'mean_reversion': 0.9,
                'trend_strength': 0.8
            }
        }

@jit(nopython=True)
def _generate_complex_regime_switching_prices(days: int):
    e = np.random.normal(0, 1, days)
    beta = np.array([0.002, -0.002, 0.0001, 0.005])
    sigma = np.array([0.01, 0.03, 0.005, 0.02])
    P = np.array([
        [0.95, 0.02, 0.02, 0.01],
        [0.03, 0.93, 0.02, 0.02],
        [0.02, 0.03, 0.94, 0.01],
        [0.01, 0.02, 0.02, 0.95],
    ], dtype=np.float64)

    regimes = np.zeros(days, dtype=np.int32)
    returns = np.zeros(days, dtype=np.float64)
    h = np.zeros(days, dtype=np.float64)

    omega = 0.00001
    alpha = 0.1
    beta_garch = 0.8

    for t in range(1, days):
        # sample next regime using our custom helper
        regimes[t] = _sample_next_regime(regimes[t - 1], P)

        # GARCH‐style volatility update
        h[t] = omega + alpha * returns[t - 1] ** 2 + beta_garch * h[t - 1]
        returns[t] = beta[regimes[t]] + np.sqrt(h[t]) * sigma[regimes[t]] * e[t]

    prices = 100 * np.exp(np.cumsum(returns))
    return prices, regimes

@jit(nopython=True)
def _generate_volume_series(days, prices, regimes):
    logp = np.log(prices)

    # -- manual diff that avoids `prepend=`
    diffs = np.empty(logp.shape, dtype=logp.dtype)
    diffs[0] = 0.0               # what “prepend=0” gave you
    diffs[1:] = logp[1:] - logp[:-1]

    price_changes = np.abs(diffs)

    base_volume   = np.random.lognormal(np.log(1e6), 0.5, days)
    regime_factor = np.where(regimes == 1, 1.5,
                     np.where(regimes == 0, 1.2, 0.8))
    volume_factor = np.exp(price_changes * 10.0)
    return base_volume * regime_factor * volume_factor


@jit(nopython=True)
def _apply_long_term_cycles(prices):
    cycle_length = 4 * 365
    n = len(prices)
    cycle = np.sin(np.linspace(0, 2 * np.pi, cycle_length))

    adjusted = np.empty_like(prices)
    for i in range(n):
        cycle_val = cycle[i % cycle_length]
        adjusted[i] = prices[i] * (1 + 0.2 * cycle_val)

    return adjusted

@jit(nopython=True)
def _sample_next_regime(prev_regime: int, P: np.ndarray) -> int:
    """
    Given a previous regime index and transition matrix P (shape (4,4)),
    return the next regime index by sampling from P[prev_regime].
    """
    u = np.random.rand()
    cum = 0.0
    for r in range(P.shape[1]):
        cum += P[prev_regime, r]
        if u < cum:
            return r
    # in case of floating‐point rounding, return last index
    return P.shape[1] - 1

"""
sim_test.py  – quick sanity-test for MarketSimulator
----------------------------------------------------
• Instantiates Config
• Builds one dummy trend-following Strategy
• Wraps that strategy in a dict  (the simulator expects   strategies: Dict[str, Strategy])
• Generates 90 days of market data
• Runs a full simulation, printing key metrics
"""

from pathlib import Path
import json

# ───────────────────────────────────────────
# build a minimal strategy for testing
# ───────────────────────────────────────────
def build_dummy_trend_strategy() -> Strategy:
    params = {
        "MOVING_AVERAGE_SHORT": 20,
        "MOVING_AVERAGE_LONG": 50,
        "TREND_STRENGTH_THRESHOLD": 0.02,
        # supply anything the simulator might access ↓
        "MAX_POSITION_SIZE": 0.1,
    }
    return Strategy(
        name="Trend_Dummy",
        description="Simple MA-crossover trend follower (test only)",
        parameters=params,
        favored_patterns=("trend_following",),
        time_frame=TimeFrame.SHORT_TERM,
        optimal_volatility=(0.0, 1.0),
        optimal_trend="bullish",
        optimal_liquidity=1.0,
    )


# ───────────────────────────────────────────
# async main
# ───────────────────────────────────────────
async def main():
    # nice logging
    logging.basicConfig(level=logging.INFO, format="%(levelname)s | %(name)s | %(message)s")
    logger = logging.getLogger("sim_test")

    # 1) config & strategy dict
    cfg = Config()
    dummy_strategy = build_dummy_trend_strategy()
    strategies = {'trend_following': dummy_strategy}

    # 2) instantiate simulator
    sim = MarketSimulator(cfg, strategies)

    # 4) run one full simulation on the dummy strategy
    results = sim.run_simulation(strategies)
    logger.info("Simulation complete. Key metrics:")
    logger.info(json.dumps({k: v for k, v in results.items() if k not in ["trade_history"]},
                           indent=2, default=str))

    # (optional) save to disk
    out = Path("sim_results.json")
    out.write_text(json.dumps(results, indent=2, default=str))
    logger.info("Saved full results to %s", out.absolute())


# ───────────────────────────────────────────
# entry-point
# ───────────────────────────────────────────
if __name__ == "__main__":
    asyncio.run(main())
